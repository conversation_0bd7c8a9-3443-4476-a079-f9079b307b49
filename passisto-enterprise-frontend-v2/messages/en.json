{"start-growing-your-business-quickly": "Start growing your business quickly", "create-an-account-and-get-access-to-all-features-for-15-days-no-credit-card-required": "Create an account and get access to all features for 15-days, No credit card required.", "enter-your-email-address-and-well-send-you-a-password-reset-code": "Enter your email address and we'll send you a password reset code", "join-100-users": "Join 100+ users", "reset-password": "Reset Password", "enter-your-email-address-and-well-send-you-a-password-reset-code-0": "Enter your email address and we'll send you a password reset code", "enter-your-new-password-and-the-reset-code-we-sent-to-your-email": "Enter your new password and the reset code we sent to your email", "new-password": "New Password", "reset-code": "Reset Code", "enter-the-code-from-your-email": "Enter the code from your email", "two-factor-authentication-is-required-but-this-ui-does-not-handle-that": "Two-factor authentication is required, but this UI does not handle that", "send-reset-code": "Send Reset Code", "send-reset-code-0": "Send Reset Code", "bg-background-shadow-none-border-rounded-lg": "bg-background shadow-none border rounded-lg", "forgot-your-password": "Forgot your password?", "personal-info": "Personal Info", "professional": "Professional", "preferences": "Preferences", "complete-your-profile": "Complete Your Profile", "tell-us-more-about-yourself-to-get-started": "Tell us more about yourself to get started", "first-name": "First Name", "john": "<PERSON>", "last-name": "Last Name", "doe": "<PERSON><PERSON>", "bio": "Bio", "tell-us-about-yourself": "Tell us about yourself", "company": "Company", "acme-inc": "Acme Inc.", "role": "Role", "software-engineer": "Software Engineer", "experience-level": "Experience Level", "select-experience-level": "Select experience level", "junior-0-2-years": "Junior (0-2 years)", "mid-level-3-5-years": "Mid-Level (3-5 years)", "senior-5-years": "Senior (5+ years)", "areas-of-interest": "Areas of Interest", "e-g-web-development-ai-ml": "e.g., Web Development, AI/ML", "linkedin-profile-optional": "LinkedIn Profile (Optional)", "previous": "Previous", "complete": "Complete", "next": "Next", "failed-to-update-usage-tracking": "Failed to update usage tracking", "interview-id-or-user-id-is-missing": "Interview ID or User ID is missing.", "generate-the-interview": "Generate The Interview", "call": "Call", "end": "End", "camera-is-turned-off": "Camera is turned off", "you": "You", "agent": "Agent", "ai-interviewer": "AI Interviewer", "listening": "Listening", "transcript": "Transcript", "ai-interviewer-0": "AI Interviewer", "no-messages-found": "No messages found", "it-looks-like-no-messages-have-been-sent-yet": "It looks like no messages have been sent yet.", "interview-questions": "Interview Questions", "questions": "Questions", "no-questions-available-for-this-interview": "No questions available for this interview", "user-not-found": "User not found.", "something-went-wrong": "Something went wrong.", "settings": "Settings", "content": "Content", "padding": "Padding", "margin": "<PERSON><PERSON>", "unit": "Unit", "alignment": "Alignment", "select-alignment": "Select alignment", "left": "Left", "center": "Center", "right": "Right", "text-color": "Text Color", "background-color": "Background Color", "font-size": "Font Size", "unit-0": "Unit", "font-weight": "Font Weight", "select-weight": "Select weight", "normal": "Normal", "bold": "Bold", "lighter": "Lighter", "bolder": "Bolder", "line-height": "Line Height", "image-url": "Image URL", "image-description": "Image description", "alt-text": "Alt Text", "width": "<PERSON><PERSON><PERSON>", "height": "Height", "auto": "Auto", "border-radius": "Border Radius", "link-image": "Link Image", "link-url": "Link URL", "button-url": "Button URL", "border-radius-0": "Border Radius", "button-width": "<PERSON><PERSON> W<PERSON>th", "select-width": "Select width", "auto-0": "Auto", "full-width": "Full Width", "border-color": "Border Color", "border-width": "Border Width", "divider-color": "Divider Color", "divider-width": "<PERSON><PERSON><PERSON>", "divider-style": "Divider Style", "select-style": "Select style", "solid": "Solid", "dashed": "Dashed", "dotted": "Dotted", "list-type": "List Type", "select-list-type": "Select list type", "bullet-list": "Bullet List", "numbered-list": "Numbered List", "list-items": "List Items", "add-item": "Add Item", "header-background-color": "Header Background Color", "table-headers": "Table Headers", "add-column": "Add Column", "remove-column": "Remove <PERSON>n", "table-rows": "Table Rows", "add-row": "Add Row", "remove-row": "Remove Row", "icon-size": "Icon Size", "icon-spacing": "Icon Spacing", "icon-color": "Icon Color", "social-media-links": "Social Media Links", "platform": "Platform", "edit": "Edit", "add-platform": "Add Platform", "advanced-ai-technology": "Advanced AI technology", "real-time-analysis": "Real-Time Analysis", "instant-feedback": "Instant feedback", "24-7-available": "24/7 Available", "access-anytime": "Access anytime", "enterprise-security": "Enterprise Security", "bank-grade-encryption": "Bank-grade encryption", "failed-to-load-subscription-information": "Failed to load subscription information", "free": "Free", "no-active-subscription": "No Active Subscription", "redirecting-to-checkout": "Redirecting to checkout...", "failed-to-initialize-checkout": "Failed to initialize checkout", "free-plan-subscription-limit-reached": "Free Plan Subscription Limit Reached", "you-can-only-subscribe-to-the-free-plan-once-even-if-your-previous-free-plan-subscription-is-no-longer-active-you-cannot-subscribe-to-the-free-plan-again-to-continue-using-the-service-please-choose-a-paid-plan": "You can only subscribe to the Free plan once. Even if your previous Free plan subscription is no longer active, you cannot subscribe to the Free plan again. To continue using the service, please choose a paid plan.", "failed-to-load-plans": "Failed to load plans", "try-again": "Try Again", "dashboard": "Dashboard", "choose-your-plan": "Choose Your Plan", "access-powerful-ai-tools-to-streamline-your-recruitment-process-and-find-the-perfect-candidates-faster": "Access powerful AI tools to streamline your recruitment process and find the perfect candidates faster", "monthly": "Monthly", "annual": "Annual", "save-20": "(Save 20%)", "free-0": "Free", "enterprise": "Enterprise", "custom": "Custom", "all-in-one-ai-recruitment-suite": "All-in-one AI recruitment suite", "plan-features": "Plan Features", "included-for-all-plans": "Included For All Plans", "no-credit-card-required": "✓ No credit card required", "cancel-anytime": "✓ Cancel anytime", "no-active-subscription-0": "No Active Subscription", "currently-subscribed": "Currently Subscribed", "start-free-trial": "Start Free Trial", "contact-sales": "Contact Sales", "subscribe-now": "Subscribe Now", "custom-solutions-for-large-teams": "Custom solutions for large teams", "tailored-pricing-for-your-specific-needs": "Tailored pricing for your specific needs", "enterprise-features": "Enterprise Features", "custom-integration": "Custom Integration", "tailored-to-your-needs": "Tailored to your needs", "dedicated-support": "Dedicated Support", "priority-24-7-support": "Priority 24/7 support", "custom-features": "Custom Features", "built-for-your-workflow": "Built for your workflow", "have-questions-about-which-plan-is-right-for-you-our-team-is-happy-to-help": "Have questions about which plan is right for you? Our team is happy to help.", "contact-support": "Contact support", "failed-to-load-subscription-details": "Failed to load subscription details", "loading-subscription-details": "Loading subscription details...", "payment-successful": "Payment Successful!", "welcome-to": "Welcome to", "your-account-has-been-successfully-upgraded": "Your account has been successfully upgraded.", "email-builder": "Email Builder", "support": "Support", "go-to-dashboard": "Go to Dashboard", "form-not-found": "Form Not Found", "the-form-youre-looking-for-doesnt-exist-or-has-been-deleted": "The form you're looking for doesn't exist or has been deleted.", "back-to-home": "Back to Home", "please-enter-both-email-and-access-code": "Please enter both email and access code", "access-granted": "Access Granted", "redirecting-you-to-your-interview": "Redirecting you to your interview...", "invalid-email-or-access-code-please-check-your-credentials-and-try-again": "Invalid email or access code. Please check your credentials and try again.", "invalid-email-or-access-code-please-check-your-credentials-and-try-again-0": "Invalid email or access code. Please check your credentials and try again.", "an-error-occurred-please-try-again-later": "An error occurred. Please try again later.", "interview-access": "Interview Access", "enter-your-email-and-access-code-to-start-your-interview": "Enter your email and access code to start your interview", "email": "Email", "access-code": "Access Code", "enter-the-code-from-your-invitation": "Enter the code from your invitation", "access-interview": "Access Interview", "having-trouble-contact-support-at": "Having trouble? Contact support at", "something-went-wrong-0": "Something went wrong!", "an-unexpected-error-occurred-please-try-again-or-return-to-the-dashboard": "An unexpected error occurred. Please try again or return to the dashboard.", "return-to-dashboard": "Return to Dashboard", "try-again-0": "Try Again", "name-must-be-at-least-2-characters": "Name must be at least 2 characters.", "project-is-required": "Project is required.", "domain-name-is-required": "Domain name is required.", "please-enter-a-valid-email-address": "Please enter a valid email address.", "api-token-is-required": "API token is required.", "update-time-must-be-at-least-1-day": "Update time must be at least 1 day.", "name-must-be-at-least-2-characters-0": "Name must be at least 2 characters.", "server-ip-is-required": "Server IP is required.", "port-is-required": "Port is required.", "username-is-required": "Username is required.", "password-is-required": "Password is required.", "update-time-must-be-at-least-1-day-0": "Update time must be at least 1 day.", "please-enter-a-valid-url": "Please enter a valid URL.", "permission-denied": "Permission denied", "you-dont-have-permission-to-access-the-integrations-page": "You don't have permission to access the integrations page.", "jira-integration": "Jira Integration", "ftp-integration": "FTP Integration", "web-integration": "Web Integration", "configure-your-jira-integration-by-providing-the-required-details-below": "Configure your Jira integration by providing the required details below.", "integration-name": "Integration Name", "product-backlog": "Product Backlog", "a-descriptive-name-for-this-integration": "A descriptive name for this integration", "project-key": "Project Key", "e-g-prd": "e.g. PRD", "this-is-the-unique": "This is the unique", "project-key-0": "project key", "e-g-prd-not-the-full-project-name": "(e.g. \"PRD\", not the full project name).", "api-token": "API Token", "you-can-generate-a-jira-api-token-from-your-atlassian-account-settings": "You can generate a Jira API token from your Atlassian account settings.", "get-your-token-here": "Get your token here", "domain-name": "Domain Name", "your-jira-instance-domain": "Your Jira instance domain", "email-associated-with-your-jira-account": "Email associated with your Jira account", "update-frequency-days": "Update Frequency (days)", "how-often-to-sync-data-minimum-1-day": "How often to sync data (minimum 1 day)", "create-jira-integration": "Create Jira Integration", "ftp-integration-0": "FTP Integration", "configure-your-ftp-integration-by-providing-the-required-details-below": "Configure your FTP integration by providing the required details below.", "financial-reports": "Financial Reports", "a-descriptive-name-for-this-integration-0": "A descriptive name for this integration", "server-ip": "Server IP", "ftp-server-address": "FTP server address", "port": "Port", "ftp-server-port-usually-21": "FTP server port (usually 21)", "username": "Username", "ftp-account-username": "FTP account username", "password": "Password", "ftp-account-password-stored-securely": "FTP account password (stored securely)", "connection-type": "Connection Type", "select-connection-type": "Select connection type", "ftp-not-encrypted": "FTP (Not Encrypted)", "sftp-secure": "SFTP (Secure)", "choose-between-ftp-and-secure-sftp": "Choose between FTP and secure SFTP.", "update-frequency-days-0": "Update Frequency (days)", "create-ftp-integration": "Create FTP Integration", "web-integration-0": "Web Integration", "configure-your-web-integration-by-providing-the-required-details-below": "Configure your Web integration by providing the required details below.", "competitor-analysis": "Competitor Analysis", "a-descriptive-name-for-this-integration-1": "A descriptive name for this integration", "the-web-url-to-fetch-data-from": "The web URL to fetch data from", "create-web-integration": "Create Web Integration", "delete-integration": "Delete Integration", "are-you-sure-you-want-to-delete-the-integration": "Are you sure you want to delete the integration \"", "this-action-cannot-be-undone": "This action cannot be undone.", "cancel": "Cancel", "delete": "Delete", "deleting": "Deleting...", "leave-blank-to-keep-current-token": "Leave blank to keep current token", "server": "Server", "leave-blank-to-keep-current-password": "Leave blank to keep current password", "secure-connection": "Secure Connection", "edit-integration": "Edit Integration", "save-changes": "Save Changes", "yes": "Yes", "no": "No", "domain": "Domain", "project": "Project", "visit-website": "Visit Website", "no-configuration-available": "No configuration available.", "integration-status-guide": "Integration Status Guide", "this-means-your-integration-just-started-collecting-information-its-the-first-step-after-setting-it-up": "This means your integration just started collecting information. It's the first step after setting it up.", "understand-what-each-status-means-in-simple-words": "Understand what each status means in simple words.", "everything-worked-perfectly-your-integration-successfully-brought-in-the-data": "Everything worked perfectly. Your integration successfully brought in the data.", "something-went-wrong-the-integration-couldnt-fetch-the-data-properly": "Something went wrong. The integration couldn’t fetch the data properly.", "the-integration-already-worked-before-and-now-its-trying-to-update-its-data-again": "The integration already worked before, and now it's trying to update its data again.", "not-updated": "Not Updated:", "the-system-tried-to-refresh-the-data-but-it-didnt-work-this-time-it-stayed-as-it-was-before": "The system tried to refresh the data, but it didn’t work this time. It stayed as it was before.", "integration": "Integration", "overview": "Overview", "configuration": "Configuration", "update-frequency": "Update Frequency", "every-integration-updatetime-days": "Every {0} days", "created-by": "Created By", "created-at": "Created At", "last-rerun": "Last Rerun", "retry": "Retry", "retrying": "Retrying ...", "status": "Status", "current-integration-status": "Current integration status", "quick-actions": "Quick Actions", "retry-integration": "Retry Integration", "you-dont-have-permission-to-access-the-interviews-page": "You don't have permission to access the interviews page.", "ai-powered-interview-practice": "AI-Powered Interview Practice", "practice-your-interview-skills-with-our-ai-interviewer-get-real-time-feedback-and-improve-your-chances-of-landing-your-dream-job": "Practice your interview skills with our AI interviewer. Get real-time feedback and improve your chances of landing your dream job.", "create-an-interview": "Create an Interview", "view-interviews-templates": "View Interviews Templates", "realistic-experience": "Realistic Experience", "simulates-real-interview-scenarios": "Simulates real interview scenarios", "instant-feedback-0": "In<PERSON> Feedback", "get-immediate-insights-on-your-performance": "Get immediate insights on your performance", "customizable": "Customizable", "choose-role-level-and-tech-stack": "Choose role, level, and tech stack", "track-progress": "Track Progress", "monitor-your-improvement-over-time": "Monitor your improvement over time", "jan": "Jan", "feb": "Feb", "mar": "Mar", "apr": "Apr", "may": "May", "jun": "Jun", "active": "Active", "inactive": "Inactive", "ai-interview": "AI Interview", "form-builder": "Form Builder", "total-teams": "Total Teams", "active-teams": "active teams", "total-users": "Total Users", "active-users": "active users", "team-activity-rate": "Team Activity Rate", "key": "%", "of-teams-have-members": "of teams have members", "avg-users-team": "Avg Users/Team", "users-per-team": "users per team", "interviews": "Interviews", "forms": "Forms", "conv-rate": "% conv. rate", "user-growth": "User Growth", "users": "Users", "team-status": "Team Status", "empty-description": "Empty description", "please-enter-a-description-of-the-email-you-want-to-create": "Please enter a description of the email you want to create.", "prompt-copied": "Prompt copied!", "you-can-now-paste-this-prompt-to-your-preferred-ai-model": "You can now paste this prompt to your preferred AI model.", "ai-email-template-generator": "AI Email Template Generator", "describe-the-email-you-want-to-create-then-use-the-generated-prompt-with-your-preferred-ai-model": "Describe the email you want to create, then use the generated prompt with your preferred AI model.", "email-description": "Email Description", "generated-prompt": "Generated Prompt", "describe-the-email-you-want-to-create-for-example-create-a-welcome-email-for-new-customers-of-my-fitness-company-fitlife-with-a-blue-color-scheme": "Describe the email you want to create. For example: 'Create a welcome email for new customers of my fitness company FitLife, with a blue color scheme.'", "description-tips": "Description Tips:", "specify-the-type-of-email-welcome-newsletter-promotion-etc": "Specify the type of email (welcome, newsletter, promotion, etc.)", "include-your-company-or-brand-name": "Include your company or brand name", "mention-any-color-preferences": "Mention any color preferences", "describe-the-tone-formal-casual-exciting": "Describe the tone (formal, casual, exciting)", "include-specific-sections-you-want-in-the-email": "Include specific sections you want in the email", "mention-any-specific-images-or-buttons-you-want": "Mention any specific images or buttons you want", "generate-ai-prompt": "Generate AI Prompt", "how-to-use-this-prompt": "How to use this prompt:", "copy-the-prompt-above": "Copy the prompt above", "paste-it-to-chatgpt-claude-or-your-preferred-ai-model": "Paste it to <PERSON>t<PERSON><PERSON>, <PERSON>, or your preferred AI model", "the-ai-will-generate-a-json-template-based-on-your-description": "The AI will generate a JSON template based on your description", "copy-the-json-response": "Copy the JSON response", "use-the-import-json-feature-in-our-email-builder-to-load-your-template": "Use the \"Import JSON\" feature in our email builder to load your template", "edit-description": "Edit Description", "copy-prompt": "Copy Prompt", "please-enter-a-description-to-enhance": "Please enter a description to enhance.", "description-enhanced": "Description enhanced!", "your-description-has-been-enhanced-with-additional-details": "Your description has been enhanced with additional details.", "an-unknown-error-occurred": "An unknown error occurred", "enhancement-failed": "Enhancement failed", "there-was-an-error-enhancing-your-description-please-try-again": "There was an error enhancing your description. Please try again.", "empty-prompt": "Empty prompt", "please-enter-a-prompt-to-generate-a-template": "Please enter a prompt to generate a template.", "template-generated": "Template generated!", "your-email-template-has-been-created-successfully": "Your email template has been created successfully.", "generation-failed": "Generation failed", "there-was-an-error-generating-your-template-please-try-again": "There was an error generating your template. Please try again.", "describe-the-type-of-email-you-want-to-create-and-our-ai-will-generate-a-template-for-you": "Describe the type of email you want to create, and our AI will generate a template for you.", "prompt": "Prompt", "preview": "Preview", "enhanced-description": "Enhanced Description", "use-this": "Use This", "error": "Error", "prompt-tips": "Prompt Tips:", "mention-if-you-want-special-elements-like-social-icons": "Mention if you want special elements like social icons", "enhancing": "Enhancing ...", "enhance-description": "Enhance Description", "generating": "Generating ...", "generate-template": "Generate Template", "regenerate": "Regenerate", "edit-template": "Edit Template", "use-template": "Use Template", "instructions": "Instructions", "drag-components-to-the-canvas-or-click-to-add-them-to-the-bottom-of-your-email": "Drag components to the canvas or click to add them to the bottom of your email.", "components": "Components", "header": "Header", "text-block": "Text Block", "image": "Image", "button": "<PERSON><PERSON>", "divider": "Divider", "list": "List", "table": "Table", "social-icons": "Social Icons", "footer": "Footer", "ai-powered-form-builder": "AI-Powered Form Builder", "create-custom-forms-with-intelligent-field-suggestions-based-on-your-business-needs": "Create custom forms with intelligent field suggestions based on your business needs", "view-your-forms-dashboard": "View Your Forms Dashboard", "please-enter-a-form-description-first": "Please enter a form description first", "failed-to-enhance-description-please-try-again": "Failed to enhance description. Please try again.", "enhanced-description-applied": "Enhanced description applied!", "please-describe-your-form-purpose": "Please describe your form purpose", "an-error-occurred-while-generating-the-form-please-try-again": "An error occurred while generating the form. Please try again.", "describe-your-form": "Describe Your Form", "tell-us-what-kind-of-form-you-need-and-our-ai-will-generate-it-for-you": "Tell us what kind of form you need, and our AI will generate it for you", "describe-your-form-purpose-e-g-job-application-form-for-a-marketing-position-event-registration-form-for-a-conference": "Describe your form purpose (e.g., 'job application form for a marketing position', 'event registration form for a conference')", "specify-the-purpose-of-the-form-job-application-event-registration-feedback-etc": "Specify the purpose of the form (job application, event registration, feedback, etc.).", "include-your-company-or-brand-name-for-customization": "Include your company or brand name for customization.", "mention-required-fields-e-g-name-email-phone-number": "Mention required fields (e.g., name, email, phone number).", "describe-any-conditional-logic-e-g-show-extra-fields-based-on-selection": "Describe any conditional logic (e.g., show extra fields based on selection).", "specify-form-actions-e-g-email-responses-save-to-database-integrate-with-apis": "Specify form actions (e.g., email responses, save to database, integrate with APIs).", "mention-if-you-need-file-uploads-e-g-resumes-images-documents": "Mention if you need file uploads (e.g., resumes, images, documents).", "describe-validation-rules-e-g-required-fields-email-format-character-limits": "Describe validation rules (e.g., required fields, email format, character limits).", "indicate-if-you-want-branding-elements-like-logos-and-theme-colors": "Indicate if you want branding elements like logos and theme colors.", "specify-if-you-need-auto-confirmation-emails-or-notifications": "Specify if you need auto-confirmation emails or notifications.", "enhanced-description-0": "Enhanced Description:", "use-this-description": "Use This Description", "enhance-description-0": "Enhance Description", "generating-form": "Generating Form...", "generate-form": "Generate Form", "url-copied-to-clipboard": "URL copied to clipboard!", "branding": "Branding", "share": "Share", "export": "Export", "brand-colors": "Brand Colors", "customize-the-colors-to-match-your-brand-identity": "Customize the colors to match your brand identity", "primary-color": "Primary Color", "color-preview": "Color Preview", "primary-button": "Primary Button", "secondary-button": "Secondary Button", "upload-your-company-logo-to-display-on-the-form": "Upload your company logo to display on the form", "logo": "Logo", "upload-logo": "Upload Logo", "logo-preview": "Logo Preview", "no-logo-uploaded": "No logo uploaded", "share-your-form": "Share Your Form", "share-your-form-with-others-to-collect-responses": "Share your form with others to collect responses", "public-form-url": "Public Form URL", "anyone-with-this-link-can-view-and-submit-the-form": "Anyone with this link can view and submit the form", "embed-code": "Embed Code", "export-options": "Export Options", "export-your-form-in-different-formats": "Export your form in different formats", "export-form-configuration": "Export form configuration", "export-form-responses": "Export form responses", "export-form-as-document": "Export form as document", "api-integration": "API Integration", "connect-your-form-to-external-services": "Connect your form to external services", "webhook-url": "Webhook URL", "form-submissions-will-be-sent-to-this-url": "Form submissions will be sent to this URL", "api-key-optional": "API Key (optional)", "save-integration-settings": "Save Integration Settings", "text-field": "Text Field", "text-area": "Text Area", "email-field": "Email Field", "dropdown": "Dropdown", "checkbox": "Checkbox", "radio-group": "Radio Group", "file-upload": "File Upload", "search": "Search", "teams": "Teams", "integrations": "Integrations", "file-storage": "File Storage", "shared-with-me": "Shared with me", "trash": "Trash", "ai-agent": "AI Agent", "files": "Files", "recent": "Recent", "roles-and-permissions": "Roles & Permissions", "chat": "Cha<PERSON>", "new-chat": "New Chat", "today": "Today", "chat-session-deleted-successfully": "Chat session deleted successfully", "failed-to-delete-chat-session": "Failed to delete chat session", "yesterday": "Yesterday", "previous-7-days": "Previous 7 Days", "previous-30-days": "Previous 30 Days", "delete-chat-session": "Delete Chat Session", "are-you-sure-you-want-to-delete-this-chat-session-this-action-cannot-be-undone": "Are you sure you want to delete this chat session? This action cannot be undone.", "delete-session": "Delete session", "knowledge-bases": "Knowledge Bases", "search-settings": "Search Settings", "configure-your-search-experience": "Configure your search experience", "knowledge-bases-0": "Knowledge Bases", "select-which-knowledge-bases-to-search-when-performing-searches": "Select which knowledge bases to search when performing searches", "error-loading-knowledge-bases": "Error loading knowledge bases:", "all": "All", "jira": "<PERSON><PERSON>", "web": "Web", "loading-knowledge-bases": "Loading knowledge bases...", "security-system": "Security System", "understanding-the-permission-model-and-access-control": "Understanding the permission model and access control.", "back-to-roles-and-permissions": "Back to Roles & Permissions", "overview-0": "Overview", "roles": "Roles", "scopes": "<PERSON><PERSON><PERSON>", "best-practices": "Best Practices", "permission-model-overview": "Permission Model Overview", "understanding-how-permissions-work-in-our-enterprise-system": "Understanding how permissions work in our enterprise system", "multi-layered-permission-system": "Multi-layered Permission System", "our-security-system-uses-a-multi-layered-approach-to-permissions-allowing-for-fine-grained-access-control-while-maintaining-flexibility-and-ease-of-management": "Our security system uses a multi-layered approach to permissions, allowing for fine-grained access control while maintaining flexibility and ease of management.", "users-are-assigned-roles-that-grant-a-set-of-permissions-roles-provide-a-baseline-of-access-appropriate-for-different-job-functions": "Users are assigned roles that grant a set of permissions. Roles provide a baseline of access appropriate for different job functions.", "users-belong-to-teams-that-grant-additional-permissions-team-permissions-are-typically-scoped-to-the-teams-resources": "Users belong to teams that grant additional permissions. Team permissions are typically scoped to the team's resources.", "user-overrides": "User Overrides", "individual-users-can-be-granted-extra-permissions-or-have-inherited-permissions-revoked-to-handle-exceptions": "Individual users can be granted extra permissions or have inherited permissions revoked to handle exceptions.", "permission-inheritance-flow": "Permission Inheritance Flow", "role-permissions": "Role Permissions", "team-permissions": "Team Permissions", "combined-permissions": "Combined Permissions", "extra-permissions": "Extra Permissions", "revoked-permissions": "Revoked Permissions", "effective-permissions": "Effective Permissions", "permission-resolution": "Permission Resolution", "when-determining-if-a-user-has-a-specific-permission-the-system": "When determining if a user has a specific permission, the system:", "collects-all-permissions-from-the-users-roles": "Collects all permissions from the user's roles", "adds-all-permissions-from-the-users-teams": "Adds all permissions from the user's teams", "adds-any-extra-permissions-granted-directly-to-the-user": "Adds any extra permissions granted directly to the user", "removes-any-permissions-explicitly-revoked-for-the-user": "Removes any permissions explicitly revoked for the user", "considers-the-scope-of-each-permission-global-team-project-self": "Considers the scope of each permission (global, team, project, self)", "role-based-access-control": "Role-Based Access Control", "how-roles-provide-a-foundation-for-permissions-in-the-system": "How roles provide a foundation for permissions in the system", "what-are-roles": "What are Roles?", "roles-are-predefined-sets-of-permissions-that-represent-common-job-functions-or-responsibility-levels-within-your-organization-each-user-can-be-assigned-one-or-more-roles": "Roles are predefined sets of permissions that represent common job functions or responsibility levels within your organization. Each user can be assigned one or more roles.", "role-structure": "Role Structure", "each-role-contains-a-collection-of-permissions-with-specific-scopes-for-example": "Each role contains a collection of permissions with specific scopes. For example:", "administrator-role": "Administrator Role", "create-user": "Create User", "global": "Global", "manage-billing": "Manage Billing", "global-0": "Global", "view-analytics": "View Analytics", "manager-role": "Manager Role", "assign-user-to-team": "Assign User to Team", "team": "Team", "benefits-of-role-based-access": "Benefits of Role-Based Access", "simplified-management": "Simplified Management", "instead-of-assigning-individual-permissions-to-each-user-you-can-assign-roles-that-contain-predefined-sets-of-permissions-making-user-management-more-efficient": "Instead of assigning individual permissions to each user, you can assign roles that contain predefined sets of permissions, making user management more efficient.", "standardization": "Standardization", "roles-ensure-that-users-with-similar-responsibilities-have-consistent-access-rights-reducing-the-risk-of-permission-inconsistencies": "Roles ensure that users with similar responsibilities have consistent access rights, reducing the risk of permission inconsistencies.", "scalability": "Scalability", "as-your-organization-grows-roles-make-it-easier-to-onboard-new-users-with-the-appropriate-access-levels-without-having-to-configure-permissions-individually": "As your organization grows, roles make it easier to onboard new users with the appropriate access levels without having to configure permissions individually.", "auditability": "Auditability", "roles-provide-a-clear-structure-for-access-rights-making-it-easier-to-audit-who-has-access-to-what-and-why-they-have-that-access": "Roles provide a clear structure for access rights, making it easier to audit who has access to what and why they have that access.", "important-note": "Important Note", "roles-are-defined-at-the-system-level-and-cannot-be-created-or-modified-by-regular-users-contact-your-system-administrator-if-you-need-a-new-role-or-modifications-to-existing-roles": "Roles are defined at the system level and cannot be created or modified by regular users. Contact your system administrator if you need a new role or modifications to existing roles.", "team-based-permissions": "Team-Based Permissions", "how-teams-provide-contextual-access-to-resources": "How teams provide contextual access to resources", "teams-and-permissions": "Teams and Permissions", "teams-or-groups-allow-you-to-organize-users-and-grant-permissions-based-on-the-resources-they-need-to-access-teams-are-particularly-useful-for-departmental-or-project-based-access-control": "Teams (or groups) allow you to organize users and grant permissions based on the resources they need to access. Teams are particularly useful for departmental or project-based access control.", "team-structure": "Team Structure", "team-members": "Team Members", "users-who-belong-to-the-team-and-inherit-its-permissions": "Users who belong to the team and inherit its permissions", "team-permissions-0": "Team Permissions", "permissions-granted-to-all-members-of-the-team-typically-scoped-to-team-resources": "Permissions granted to all members of the team, typically scoped to team resources", "resource-scope": "Resource Scope", "the-specific-resources-projects-data-etc-that-the-team-has-access-to": "The specific resources (projects, data, etc.) that the team has access to", "engineering-team": "Engineering Team", "team-members-0": "Team Members:", "john-doe": "<PERSON>", "jane-smith": "<PERSON>", "robert-johnson": "<PERSON>", "team-permissions-1": "Team Permissions:", "create-data-provider": "Create Data Provider", "team-0": "Team", "view-analytics-0": "View Analytics", "team-1": "Team", "team-permission-scopes": "Team Permission Scopes", "team-permissions-are-typically-scoped-to-the-teams-resources-but-they-can-also-have-different-scope-types": "Team permissions are typically scoped to the team's resources, but they can also have different scope types:", "team-scope": "Team Scope", "permissions-apply-only-to-resources-owned-by-the-team-this-is-the-most-common-scope-for-team-permissions": "Permissions apply only to resources owned by the team. This is the most common scope for team permissions.", "global-scope": "Global Scope", "some-team-permissions-may-have-global-scope-allowing-team-members-to-perform-actions-across-the-entire-system": "Some team permissions may have global scope, allowing team members to perform actions across the entire system.", "project-scope": "Project Scope", "permissions-may-be-limited-to-specific-projects-that-the-team-is-working-on-even-if-those-projects-are-shared-with-other-teams": "Permissions may be limited to specific projects that the team is working on, even if those projects are shared with other teams.", "team-vs-role-permissions": "Team vs. Role Permissions", "while-roles-define-what-a-user-can-do-based-on-their-job-function-teams-define-what-resources-they-can-access-this-combination-provides-a-flexible-and-powerful-access-control-system": "While roles define what a user can do based on their job function, teams define what resources they can access. This combination provides a flexible and powerful access control system:", "a-user-with-the-manager-role-in-the-engineering-team-can-manage-engineering-resources": "A user with the \"Manager\" role in the \"Engineering\" team can manage engineering resources", "the-same-user-in-the-marketing-team-can-manage-marketing-resources": "The same user in the \"Marketing\" team can manage marketing resources", "different-teams-can-have-different-permission-sets-based-on-their-needs": "Different teams can have different permission sets based on their needs", "permission-scopes": "Permission Scopes", "understanding-how-permission-scopes-limit-the-reach-of-permissions": "Understanding how permission scopes limit the reach of permissions", "what-are-permission-scopes": "What are Permission Scopes?", "permission-scopes-define-the-boundaries-within-which-a-permission-applies-they-allow-for-fine-grained-control-over-what-resources-a-user-can-access-with-a-given-permission": "Permission scopes define the boundaries within which a permission applies. They allow for fine-grained control over what resources a user can access with a given permission.", "scope-types": "Scope Types", "permission-applies-across-the-entire-system-to-all-resources-of-the-relevant-type": "Permission applies across the entire system, to all resources of the relevant type.", "permission-applies-only-to-resources-owned-by-or-associated-with-the-users-team-s": "Permission applies only to resources owned by or associated with the user's team(s).", "permission-applies-only-to-specific-projects-regardless-of-team-ownership": "Permission applies only to specific projects, regardless of team ownership.", "permission-applies-only-to-resources-created-by-or-directly-assigned-to-the-user": "Permission applies only to resources created by or directly assigned to the user.", "scope-examples": "Scope Examples", "view-analytics-with-global-scope": "View Analytics with GLOBAL scope", "user-can-view-analytics-for-all-teams-and-projects-in-the-system": "User can view analytics for all teams and projects in the system.", "create-data-provider-with-team-scope": "Create Data Provider with TEAM scope", "user-can-create-data-providers-only-for-their-team-s": "User can create data providers only for their team(s).", "export-data-with-project-scope": "Export Data with PROJECT scope", "user-can-export-data-only-from-specific-projects-they-have-access-to": "User can export data only from specific projects they have access to.", "manage-profile-with-self-scope": "Manage Profile with SELF scope", "user-can-only-manage-their-own-profile-information": "User can only manage their own profile information.", "scope-resolution": "Scope Resolution", "when-a-user-has-the-same-permission-with-different-scopes-e-g-from-different-roles-or-teams-the-system-uses-the-broadest-scope": "When a user has the same permission with different scopes (e.g., from different roles or teams), the system uses the broadest scope:", "scope-hierarchy-broadest-to-narrowest": "Scope Hierarchy (Broadest to Narrowest)", "example-scope-resolution": "Example: Scope Resolution", "if-a-user-has-the-view-analytics-permission-from": "If a user has the \"View Analytics\" permission from:", "their-manager-role-with-team-scope": "Their \"Manager\" role with TEAM scope", "the-leadership-team-with-global-scope": "The \"Leadership\" team with GLOBAL scope", "the-effective-scope-will-be-global-as-its-broader-than-team": "The effective scope will be GLOBAL, as it's broader than TEAM.", "security-best-practices": "Security Best Practices", "guidelines-for-implementing-effective-access-control": "Guidelines for implementing effective access control", "principle-of-least-privilege": "Principle of Least Privilege", "always-grant-users-the-minimum-permissions-necessary-to-perform-their-job-functions-this-reduces-the-risk-of-accidental-or-intentional-misuse-of-privileges": "Always grant users the minimum permissions necessary to perform their job functions. This reduces the risk of accidental or intentional misuse of privileges.", "role-assignment": "Role Assignment", "do": "Do", "assign-roles-based-on-job-responsibilities": "Assign roles based on job responsibilities", "regularly-review-and-audit-role-assignments": "Regularly review and audit role assignments", "remove-unnecessary-roles-when-job-functions-change": "Remove unnecessary roles when job functions change", "use-the-most-restrictive-role-that-meets-the-users-needs": "Use the most restrictive role that meets the user's needs", "assign-administrator-roles-unnecessarily": "Assign administrator roles unnecessarily", "create-roles-with-excessive-permissions": "Create roles with excessive permissions", "assign-multiple-roles-when-one-would-suffice": "Assign multiple roles when one would suffice", "leave-unused-roles-assigned-to-users": "Leave unused roles assigned to users", "team-management": "Team Management", "organize-teams-based-on-functional-areas": "Organize teams based on functional areas", "limit-team-permissions-to-necessary-resources": "Limit team permissions to necessary resources", "regularly-review-team-memberships": "Regularly review team memberships", "use-team-scoped-permissions-when-possible": "Use team-scoped permissions when possible", "create-teams-with-overlapping-responsibilities": "Create teams with overlapping responsibilities", "grant-global-permissions-to-teams-unnecessarily": "Grant global permissions to teams unnecessarily", "key-0": "→", "add-users-to-teams-they-dont-need-to-be-in": "Add users to teams they don't need to be in", "create-teams-solely-for-permission-management": "Create teams solely for permission management", "permission-overrides": "Permission Overrides", "use-permission-overrides-sparingly-and-only-when-necessary-they-should-be-the-exception-not-the-rule": "Use permission overrides sparingly and only when necessary. They should be the exception, not the rule.", "when-to-use-permission-overrides": "When to Use Permission Overrides", "extra-permissions-0": "Extra Permissions", "grant-when-a-user-needs-a-specific-permission-that-isnt-included-in-their-roles-or-teams-but-doesnt-warrant-a-role-change": "Grant when a user needs a specific permission that isn't included in their roles or teams, but doesn't warrant a role change.", "revoked-permissions-0": "Revoked Permissions", "use-when-a-user-has-a-role-or-team-membership-that-grants-permissions-they-shouldnt-have-but-they-still-need-the-role-or-team-for-other-permissions": "Use when a user has a role or team membership that grants permissions they shouldn't have, but they still need the role or team for other permissions.", "caution-with-overrides": "Caution with Overrides", "excessive-use-of-permission-overrides-can-make-your-security-model-difficult-to-understand-and-audit-if-you-find-yourself-frequently-using-overrides-consider": "Excessive use of permission overrides can make your security model difficult to understand and audit. If you find yourself frequently using overrides, consider:", "reviewing-and-adjusting-your-role-definitions": "Reviewing and adjusting your role definitions", "creating-more-specialized-teams-with-appropriate-permissions": "Creating more specialized teams with appropriate permissions", "implementing-a-more-granular-permission-model": "Implementing a more granular permission model", "regular-security-audits": "Regular Security Audits", "conduct-regular-audits-of-your-permission-system-to-ensure-it-remains-effective-and-secure": "Conduct regular audits of your permission system to ensure it remains effective and secure.", "audit-checklist": "Audit Checklist", "review-user-role-assignments-for-appropriateness": "Review user role assignments for appropriateness", "verify-team-memberships-and-permissions": "Verify team memberships and permissions", "identify-and-review-permission-overrides": "Identify and review permission overrides", "check-for-users-with-excessive-permissions": "Check for users with excessive permissions", "remove-permissions-for-inactive-users": "Remove permissions for inactive users", "document-any-exceptions-to-standard-security-policies": "Document any exceptions to standard security policies", "permission-denied-0": "Permission denied", "you-dont-have-permission-to-edit-teams": "You don't have permission to edit teams.", "error-loading-data": "Error loading data", "there-was-an-error-loading-the-team-data": "There was an error loading the team data.", "team-name-is-required": "Team name is required.", "team-description-is-required": "Team description is required.", "authentication-required": "Authentication required", "team-updated": "Team updated", "update-failed": "Update failed", "there-was-an-error-updating-the-team-information": "There was an error updating the team information.", "loading-team": "Loading team...", "back-to-team-details": "Back to Team Details", "edit-team": "Edit Team", "update-team-information-and-permissions": "Update team information and permissions.", "form-errors": "Form errors:", "has-errors": "Has errors:", "the-team-already-exists": "The team already exists", "please-fill-in-the-following-fields": "Please fill in the following fields:", "team-details": "Team Details", "team-name": "Team Name", "description": "Description", "team-permissions-2": "Team Permissions", "permissions-assigned-to-this-team-will-be-granted-to-all-team-members-unless-explicitly-revoked-for-specific-users": "Permissions assigned to this team will be granted to all team members, unless explicitly revoked for specific users.", "you-dont-have-permission-to-view-team-details": "You don't have permission to view team details.", "loading-team-details": "Loading team details...", "back-to-teams": "Back to Teams", "manage-members": "Manage Members", "edit-team-0": "Edit Team", "view-integrations": "View Integrations", "team-details-0": "Team Details", "team-information-and-statistics": "Team information and statistics", "no-description-provided": "No description provided", "team-members-1": "team members", "permissions-assigned": "permissions assigned", "last-updated": "Last updated:", "view-team-members": "View Team Members", "team-information": "Team Information", "detailed-information-about-this-team": "Detailed information about this team", "permissions": "Permissions", "recent-activity": "Recent Activity", "team-permissions-3": "Team Permissions", "permissions-assigned-to-this-team-are-granted-to-all-team-members-unless-explicitly-revoked-for-specific-users": "Permissions assigned to this team are granted to all team members, unless explicitly revoked for specific users.", "no-permissions-assigned": "No permissions assigned", "this-team-doesnt-have-any-permissions-assigned-yet": "This team doesn't have any permissions assigned yet.", "edit-permissions": "Edit Permissions", "activity-log-coming-soon": "Activity log coming soon", "team-activity-tracking-will-be-available-in-a-future-update": "Team activity tracking will be available in a future update.", "you-dont-have-permission-to-view-team-integrations": "You don't have permission to view team integrations.", "failed-to-load-team-integrations": "Failed to load team integrations", "active-0": "Active", "updating": "Updating", "failed": "Failed", "not-updated-0": "Not Updated", "team-integrations": "Team Integrations", "manage-integrations": "Manage Integrations", "error-loading-integrations": "Error loading integrations", "no-integrations-found": "No integrations found", "this-team-doesnt-have-any-integrations-assigned-yet": "This team doesn't have any integrations assigned yet", "assign-integrations": "Assign Integrations", "configuration-details-not-available": "Configuration details not available", "rows-per-page": "Rows per page:", "showing": "Showing", "first-page": "First page", "previous-page": "Previous page", "next-page": "Next page", "last-page": "Last page", "no-team-members-found": "No team members found", "this-team-has-no-members-or-none-match-your-search": "This team has no members or none match your search", "add-members": "Add Members", "more-options": "More options", "remove-from-team": "<PERSON><PERSON><PERSON> from team", "name": "Name", "actions": "Actions", "no-members-found": "No members found.", "remove": "Remove", "error-loading-team-data": "Error loading team data", "failed-to-load-team-and-member-data": "Failed to load team and member data.", "authentication-required-0": "Authentication required", "member-removed": "Member removed", "error-removing-member": "Error removing member", "failed-to-remove-team-member": "Failed to remove team member.", "members-added": "Members added", "error-adding-members": "Error adding members", "failed-to-add-team-members": "Failed to add team members.", "loading-team-members": "Loading Team Members...", "team-not-found": "Team not found", "back-to-team-details-0": "Back to Team Details", "members": "Members", "manage-members-of-this-team": "Manage members of this team.", "add-members-0": "Add Members", "add-team-members": "Add Team Members", "select-users-to-add-to-the": "Select users to add to the", "search-users": "Search users...", "no-available-users-found": "No available users found.", "add": "Add", "search-members": "Search members...", "remove-team-member": "Remove Team Member", "are-you-sure-you-want-to-remove": "Are you sure you want to remove", "member": "Member", "no-teams-found": "No teams found", "no-teams-match-your-search-criteria-or-no-teams-have-been-created-yet": "No teams match your search criteria or no teams have been created yet.", "create-team": "Create Team", "open-menu": "Open menu", "actions-0": "Actions", "view-details": "View Details", "edit-0": "Edit", "members-0": "Members", "assign-integration": "Assign Integration", "cannot-delete-default-team": "Cannot Delete Default Team", "no-description-provided-0": "No description provided", "no-permissions": "No permissions", "details": "Details", "no-teams-found-0": "No teams found.", "empty": "Empty", "view-details-0": "View details", "manage-members-0": "Manage members", "open-menu-0": "Open menu", "permission-denied-1": "Permission denied", "you-dont-have-permission-to-access-the-users-page": "You don't have permission to access the users page.", "error-loading-users": "Error loading users", "user-deleted": "User deleted", "error-deleting-user": "Error deleting user", "error-updating-user-status": "Error updating user status", "loading-users": "Loading Users...", "users-0": "Users", "manage-your-organizations-users": "Manage your organization's users.", "add-user": "Add User", "filter": "Filter", "filter-by-role": "Filter by Role", "select-role": "Select role", "all-roles": "All Roles", "filter-by-team": "Filter by Team", "select-team": "Select team", "all-teams": "All Teams", "delete-user": "Delete User", "are-you-sure-you-want-to-delete": "Are you sure you want to delete", "this-action-cannot-be-undone-0": "? This action cannot be undone.", "ban-user": "Ban User", "activate-user": "Activate User", "failed-to-fetch-integration-metrics": "Failed to fetch integration metrics", "loading-integration-metrics": "Loading integration metrics...", "data-integrations": "Data Integrations", "manage-and-connect-your-enterprise-data-from-a-variety-of-sources-all-in-one-place": "Manage and connect your enterprise data from a variety of sources — all in one place.", "new-integration": "New Integration", "total-integrations": "Total Integrations", "active-data-connections": "Active data connections", "currently-processing": "Currently processing", "completed": "Completed", "successfully-processed": "Successfully processed", "needs-attention": "Needs attention", "all-integrations": "All Integrations", "view-filter-and-manage-your-data-integrations": "View, filter, and manage your data integrations", "failed-0": "Failed", "loading": "Loading", "refreshing": "Refreshing", "all-sources": "All sources", "all-statuses": "All statuses", "not-updated-1": "Not updated", "filter-by-name": "Filter by name...", "no-integrations-found-0": "No integrations found.", "integration-s-total": "integration(s) total", "loading-integrations": "Loading integrations...", "columns": "Columns", "filter-by-status": "Filter by status", "source-type": "Source Type", "failed-to-fetch-integrations": "Failed to fetch integrations", "filter-by-source": "Filter by source", "are-you-sure-you-want-to-delete-this-form": "Are you sure you want to delete this form?", "your-forms-dashboard": "Your Forms Dashboard", "create-new-form": "Create New Form", "no-forms-yet": "No Forms Yet", "you-havent-created-any-forms-yet-use-the-form-builder-to-get-started": "You haven't created any forms yet. Use the form builder to get started.", "create-your-first-form-by-describing-what-you-need-and-our-ai-will-generate-it-for-you": "Create your first form by describing what you need, and our AI will generate it for you.", "create-your-first-form": "Create Your First Form", "no-description": "No description", "view": "View", "responses": "Responses", "you-dont-have-permission-to-assign-integrations-to-teams": "You don't have permission to assign integrations to teams.", "failed-to-load-data": "Failed to load data", "integration-assigned": "Integration assigned", "integration-has-been-successfully-assigned-to-currentteam-name": "Integration has been successfully assigned to {0}.", "failed-to-assign-integration": "Failed to assign integration", "an-unexpected-error-occurred": "An unexpected error occurred.", "error-assigning-integration": "Error assigning integration:", "syncing": "Syncing", "unknown": "Unknown", "assign-integrations-to": "Assign integrations to", "please-try-refreshing-the-page": "Please try refreshing the page", "refresh": "Refresh", "no-integrations-available": "No integrations available", "there-are-no-integrations-to-assign-at-this-time": "There are no integrations to assign at this time", "server-0": "Server:", "update-time": "Update Time:", "assigning": "Assigning...", "assign-to-team": "Assign to Team", "are-you-sure-you-want-to-assign": "Are you sure you want to assign", "this-will-give-all-team-members-access-to-this-integration": "? This will give all team members access to this integration.", "please-complete-onboarding-first": "Please complete onboarding first", "error-verifying-account-status": "Error verifying account status", "verifying-account-status": "Verifying account status...", "switch-language": "Switch language", "francais": "🇫🇷 Français", "english": "🇺🇸 English", "error-while-fetching-the-templates": "Error while fetching the templates", "no-candidates-added": "No candidates added.", "missing-template": "Missing Template", "please-select-an-interview-template": "Please select an interview template.", "invitations-sent-successfully": "Invitations sent successfully!", "missing-file": "Missing File", "please-upload-a-csv-file-with-email-addresses": "Please upload a CSV file with email addresses.", "file-uploaded": "File Uploaded", "csv-file-processed-successfully-interviews-will-be-sent-to-the-candidates": "CSV file processed successfully. Interviews will be sent to the candidates.", "upload-failed": "Upload Failed", "there-was-an-error-processing-the-file-please-try-again": "There was an error processing the file. Please try again.", "template-deleted": "Template Deleted", "failed-to-delete-the-template-please-try-again": "Failed to delete the template. Please try again.", "delete-error": "Delete error:", "server-error": "Server Error", "something-went-wrong-while-deleting-the-template": "Something went wrong while deleting the template.", "loading-interviews": "Loading interviews...", "interview-templates": "Interview Templates", "create-and-manage-interview-templates-to-send-to-candidates": "Create and manage interview templates to send to candidates", "new-template": "New Template", "filter-templates": "Filter Templates", "find-templates-by-role-type-or-level": "Find templates by role, type, or level", "search-by-role-or-tech": "Search by role or tech...", "type": "Type", "select-type": "Select type", "all-types": "All Types", "technical": "Technical", "behavioral": "Behavioral", "system-design": "System Design", "mixed": "Mixed", "level": "Level", "select-level": "Select level", "all-levels": "All Levels", "junior": "Junior", "senior": "Senior", "mid-level": "Mid-level", "lead": "Lead", "tech-stack": "Tech Stack:", "more-questions": "more questions", "created-by-0": "Created by:", "send": "Send", "InterviewDialog": {"description": "Send the \"{role} {level}\" interview to candidates."}, "error-uploading-file": "Error uploading file:", "missing-information": "Missing Information", "please-fill-in-all-required-fields-before-saving": "Please fill in all required fields before saving.", "template-updated": "Template Updated", "your-interview-template-has-been-updated-successfully": "Your interview template has been updated successfully.", "update-failed-0": "Update Failed", "something-went-wrong-please-try-again": "Something went wrong. Please try again.", "loading-template": "Loading template...", "template-not-found": "Template Not Found", "we-couldnt-find-the-template-youre-looking-for-it-may-have-been-deleted-or-doesnt-exist": "We couldn't find the template you're looking for. It may have been deleted or doesn't exist.", "return-to-templates": "Return to Templates", "edit-interview-template": "Edit Interview Template", "role-0": "Role", "e-g-frontend-developer": "e.g. Frontend Developer", "interview-type": "Interview Type", "tech-stack-0": "Tech Stack", "e-g-react": "e.g. <PERSON><PERSON>", "enter-a-question": "Enter a question...", "no-questions-added-yet-add-some-questions-to-create-your-interview-template": "No questions added yet. Add some questions to create your interview template.", "save-template": "Save Template", "upload-failed-0": "Upload Failed", "loading-candidates": "Loading candidates...", "interview-not-found": "Interview Not Found", "we-couldnt-find-the-interview-youre-looking-for-it-may-have-been-deleted-or-doesnt-exist": "We couldn't find the interview you're looking for. It may have been deleted or doesn't exist.", "return-to-interviews": "Return to Interviews", "interview-candidates": "Interview Candidates", "invite-candidates": "In<PERSON><PERSON>", "dialogDescription": "Send the \"{role} {level}\" interview to candidates.", "manual-entry": "Manual Entry", "csv-upload": "CSV Upload", "full-name": "Full Name", "add-candidate": "Add Candidate", "send-invitations": "Send Invitations", "upload-csv-file": "Upload CSV File", "drag-and-drop-a-csv-file-or-click-to-browse": "Drag and drop a CSV file, or click to browse", "browse-files": "Browse Files", "csv-file-should-have-2-columns-full-name-and-email": "CSV file should have 2 columns: \"Full Name\" and \"Email\"", "upload-and-send": "Upload and Send", "interview-details": "Interview Details", "information-about-this-interview": "Information about this interview", "filter-candidates": "Filter Candidates", "find-candidates-by-name-email-or-status": "Find candidates by name, email, or status", "search-0": "Search", "search-by-name-or-email": "Search by name or email...", "status-0": "Status", "select-status": "Select status", "all-statuses-0": "All Statuses", "pending": "Pending", "in-progress": "In Progress", "completed-0": "Completed", "candidate": "Candidate", "status-1": "Status", "score": "Score", "completed-at": "Completed At", "no-candidates-found": "No candidates found", "copy": "Copy", "drag-components-here": "Drag components here", "or-select-components-from-the-sidebar": "or select components from the sidebar", "pick-a-color": "Pick a color", "your-email-header": "Your Email Header", "this-is-a-paragraph-of-text-you-can-edit-this-text-to-add-your-own-content": "This is a paragraph of text. You can edit this text to add your own content.", "click-me": "Click Me", "c-2025-your-company-all-rights-reserved": "© 2025 Your Company. All rights reserved.", "template-saved": "Template saved", "template-name-has-been-saved-successfully": "Template \"{0}\" has been saved successfully.", "there-was-an-error-saving-your-template": "There was an error saving your template.", "template-loaded": "Template loaded", "template-name-has-been-loaded-successfully": "Template \"{0}\" has been loaded successfully.", "error-loading-template": "Error loading template", "there-was-an-error-loading-your-template": "There was an error loading your template.", "error-saving-template": "Error saving template", "preview-and-send": "Preview & Send", "email-preview": "Email Preview", "preview-how-your-email-will-look-to-recipients": "Preview how your email will look to recipients", "email-preview-0": "Email Preview", "close": "Close", "no-components-added-yet": "No components added yet", "sendEmailDialog": {"title": "Send Email", "description": "Send your email template to a recipient. Fill in the details below.", "toLabel": "To", "fromNameLabel": "From Name", "fromEmailLabel": "From Email", "subjectLabel": "Subject", "toPlaceholder": "<EMAIL>", "fromNamePlaceholder": "Your Name or Company or Email", "fromEmailPlaceholder": "<EMAIL>", "subjectPlaceholder": "Email Subject", "cancelButton": "Cancel", "sendButton": "Send Email", "sendingButton": "Sending...", "recipientSubjectRequiredError": "Recipient email and subject are required", "emailContentError": "Email content could not be generated", "htmlGenerationError": "Error generating email HTML. Please try again.", "sendSuccessToastTitle": "<PERSON>ail sent successfully", "sendSuccessToastDescription": "Email has been sent to {recipientEmail}", "sendFailToastTitle": "Failed to send email", "sendFailToastDescription": "There was an error sending your email. Please try again."}, "toolbar": {"designMode": "Design Mode", "previewMode": "Preview Mode", "htmlCode": "HTML Code", "desktopView": "Desktop View", "tabletView": "Tablet View", "mobileView": "Mobile View", "undoButton": "Undo", "redoButton": "Redo", "viewAllTemplatesButton": "View All Templates", "copyHtmlButton": "Copy HTML", "htmlCopiedToastTitle": "HTML copied", "htmlCopiedToastDescription": "The HTML code has been copied to your clipboard.", "importJsonTemplateButton": "Import JSON Template", "importJsonDialogTitle": "Import JSON Template", "pasteJsonLabel": "Paste JSON Template", "pasteJsonPlaceholder": "[{\"id\":\"1\",\"type\":\"header\",\"content\":\"Welcome\",\"settings\":{...}}]", "pasteJsonDescription": "Paste the JSON array generated by an AI model based on your prompt.", "importButton": "Import", "cancelButton": "Cancel", "templateImportedToastTitle": "Template imported", "templateImportedToastDescription": "The JSON template has been successfully imported.", "invalidJsonToastTitle": "Invalid JSON", "invalidJsonToastDescription": "Please provide a valid JSON array of components.", "saveTemplateButton": "Save Template", "saveTemplateDialogTitle": "Save Template", "templateNameLabel": "Template Name", "templateNamePlaceholder": "My Email Template", "saveButton": "Save", "loadTemplateButton": "Load Template", "loadTemplateDialogTitle": "Load Template", "selectTemplateLabel": "Select Template", "selectTemplatePlaceholder": "Select a template", "loadButton": "Load"}, "editorPage": {"loadingEditor": "Loading editor...", "backToGeneratorButton": "Back to Generator", "permissionDeniedToastTitle": "Permission denied", "permissionDeniedToastDescription": "You don't have permission to access the email builder page."}, "previewPage": {"loadingEditor": "Loading editor...", "errorTitle": "Error", "failedToLoadTemplate": "Failed to load template. Please try again later.", "goBackToTemplates": "Go back to templates", "templateNotFoundTitle": "Template Not Found", "templateNotFoundDescription": "The template you are looking for does not exist or has been deleted.", "editTemplateButton": "Edit Template", "noHtmlPreview": "No HTML preview available for this template.", "editToGenerateHtml": "Edit to generate HTML"}, "templatesPage": {"permissionDeniedToastTitle": "Permission denied", "permissionDeniedToastDescription": "You don't have permission to access the email builder page.", "failedToLoadTemplates": "Failed to load templates. Please try again later.", "templateDeletedToastTitle": "Template deleted", "templateDeletedToastDescription": "The template has been successfully deleted.", "errorToastTitle": "Error", "deleteErrorToastDescription": "Failed to delete the template. Please try again.", "emailTemplatesTitle": "Email Templates", "createNewTemplateButton": "Create New Template", "noDescription": "No description", "componentsCount": "{count} components", "editMenuItem": "Edit", "previewMenuItem": "Preview", "duplicateMenuItem": "Duplicate", "deleteMenuItem": "Delete", "previewTooltip": "Preview this template", "editTooltip": "Edit this template", "noTemplatesFoundTitle": "No templates found", "noTemplatesFoundDescription": "You haven't created any email templates yet.", "createFirstTemplateButton": "Create your first template", "deleteAlertDialogTitle": "Are you sure?", "deleteAlertDialogDescription": "This action cannot be undone. This will permanently delete the template and remove it from our servers.", "cancelButton": "Cancel", "deleteButton": "Delete"}, "emailBuilderPage": {"permissionDeniedToastTitle": "Permission denied", "permissionDeniedToastDescription": "You don't have permission to access the email builder page.", "viewTemplatesButton": "View Templates", "aiTemplateGenerator": {"heading": "AI Email Template Generator", "subheading": "Generate beautiful email templates with AI in seconds.", "emailSubjectLabel": "Email Subject", "emailSubjectPlaceholder": "e.g., 'Welcome to our newsletter', 'New product launch announcement'", "emailDescriptionLabel": "Email Description (Optional)", "emailDescriptionPlaceholder": "Provide more details for the AI, e.g., 'A welcome email for new sign-ups, highlighting our best features and a call to action to explore products.'", "enhanceDescriptionButton": "Enhance Description with AI", "generateTemplateButton": "Generate Template", "generatingTemplateButton": "Generating Template...", "aiProcessingToastTitle": "AI is processing...", "aiProcessingToastDescription": "Your email template is being generated. This might take a moment.", "errorGeneratingTemplateToastTitle": "Error generating template", "errorGeneratingTemplateToastDescription": "There was an error generating your email template. Please try again.", "errorEnhancingDescriptionToastTitle": "Error enhancing description", "errorEnhancingDescriptionToastDescription": "There was an an error enhancing your description. Please try again.", "noSubjectError": "Please provide an email subject."}, "template-saved": "Template saved", "template-name-has-been-saved-successfully": "Template \"{name}\" has been saved successfully.", "error-saving-template": "Error saving template", "there-was-an-error-saving-your-template": "There was an error saving your template.", "template-loaded": "Template loaded", "template-name-has-been-loaded-successfully": "Template \"{name}\" has been loaded successfully.", "error-loading-template": "Error loading template", "there-was-an-error-loading-your-template": "There was an error loading your template.", "email-template-title": "<PERSON>ail Te<PERSON>late", "your-email-header": "Your Email Header", "this-is-a-paragraph-of-text-you-can-edit-this-text-to-add-your-own-content": "This is a paragraph of text. You can edit this text to add your own content.", "click-me": "Click Me", "c-2025-your-company-all-rights-reserved": "© 2025 Your Company. All rights reserved.", "email-image-alt-text": "Email image"}, "aiFieldSuggestions": {"textareaPlaceholder": "Describe your form purpose (e.g., 'job application form for a marketing position', 'event registration form for a conference')", "describeFormPurposeError": "Please describe your form purpose", "generatingButton": "Generating...", "generateFieldsButton": "Generate Fields", "suggestionsCardTitle": "{title}", "suggestionsCardFieldsCount": "{count} fields suggested", "applySuggestionsButton": "Apply Suggestions", "generateSuggestionsFailedError": "Failed to generate suggestions. Please try again.", "anErrorOccurred": "An error occurred while generating suggestions. Please try again."}, "dashboardPreview": {"loadingForms": "Loading forms...", "yourFormsTitle": "Your Forms", "noFormsCardTitle": "Your Forms", "noFormsCardDescription": "You haven't created any forms yet. Use the form builder above to get started.", "createdLabel": "Created:", "noDescription": "No description", "fieldsCount": "{count} fields", "editButton": "Edit", "viewButton": "View", "responsesButton": "Responses", "deleteConfirmation": "Are you sure you want to delete this form?", "deleteButtonTooltip": "Delete"}, "designTab": "Design", "previewTab": "Preview", "settingsTab": "Settings", "saveButton": "Save", "viewButton": "View", "addFieldsHeading": "Add Fields", "formSavedSuccess": "Form saved successfully!", "exportPdfFutureUpdate": "Export to PDF will be implemented in a future update.", "exportCsvFutureUpdate": "Export to CSV will be implemented in a future update.", "interviewTemplatesPage": {"loading-interviews": "Loading interview templates...", "permissionDeniedToastTitle": "Permission denied", "permissionDeniedToastDescription": "You don't have permission to access the interviews page.", "pageTitle": "Interview Templates", "pageDescription": "Create and manage interview templates to send to candidates", "newTemplateButton": "New Template", "filterTemplatesTitle": "Filter Templates", "filterTemplatesDescription": "Find templates by role, type, or level", "search": "Search", "search-by-role-or-tech": "Search by role or technology", "type": "Type", "select-type": "Select type", "all-types": "All Types", "technical": "Technical", "behavioral": "Behavioral", "system-design": "System Design", "mixed": "Mixed", "level": "Level", "select-level": "Select level", "all-levels": "All Levels", "junior": "Junior", "mid-level": "Mid-level", "senior": "Senior", "lead": "Lead", "techStackLabel": "Tech Stack:", "questionsLabel": "Questions: {count}", "moreQuestions": "+{count} more questions", "createdBy": "Created by:", "created": "Created:", "candidatesCount": "{count} candidates", "completedCount": "{count} completed", "sendButton": "Send", "viewButton": "View", "editButton": "Edit", "deleteButton": "Delete", "sendInterviewDialogTitle": "In<PERSON><PERSON>", "sendInterviewDialogDescription": "Send the \"{role} {level}\" interview to candidates.", "manualEntryTab": "Manual Entry", "csvUploadTab": "CSV Upload", "fullNamePlaceholder": "Full Name", "emailPlaceholder": "Email", "addCandidateButton": "Add Candidate", "sendInvitationsButton": "Send Invitations", "uploadCsvFileLabel": "Upload CSV File", "csvDropzoneText": "Drag and drop a CSV file, or click to browse", "browseFilesButton": "Browse Files", "selectedFile": "Selected: {fileName}", "csvFormatHint": "CSV file should have 2 columns: \"Full Name\" and \"Email\"", "uploadAndSendButton": "Upload and Send", "missingInformationToastTitle": "Missing Information", "missingInformationToastDescriptionEmails": "Please enter at least one email address.", "missingTemplateToastTitle": "Missing Template", "missingTemplateToastDescription": "Please select an interview template.", "invitationsSentSuccessToast": "Invitations sent successfully!", "errorSendingCandidates": "Error sending candidates:", "unexpectedError": "An unexpected error occurred.", "missingFileToastTitle": "Missing File", "missingFileToastDescription": "Please upload a CSV file with email addresses.", "fileUploadedToastTitle": "File Uploaded", "fileUploadedToastDescription": "CSV file processed successfully. Interviews will be sent to the candidates.", "uploadFailedToastTitle": "Upload Failed", "uploadFailedToastDescription": "There was an error processing the file. Please try again.", "templateDeletedToastTitle": "Template Deleted", "templateDeletedToastDescription": "\"{role} {level}\" template has been deleted.", "errorDeletingTemplateToastTitle": "Error", "errorDeletingTemplateToastDescription": "Failed to delete the template. Please try again.", "serverErrorToastTitle": "Server Error", "serverErrorToastDescription": "Something went wrong while deleting the template.", "noTemplatesFoundTitle": "No templates found", "noTemplatesFoundDescription": "Create your first interview template to get started."}, "createInterviewTemplatePage": {"permissionDeniedToastTitle": "Permission denied", "permissionDeniedToastDescription": "You don't have permission to access this page.", "pageTitle": "Create Interview Template", "pageDescription": "Design a template for AI-powered interviews - questions will be generated automatically", "basicInformationTitle": "Basic Information", "roleTitleLabel": "Role Title", "roleTitlePlaceholder": "e.g. Senior Frontend Developer", "requiredField": " (required)", "experienceLevelLabel": "Experience Level", "selectExperienceLevelPlaceholder": "Select experience level", "juniorLevel": "Junior (0-2 years)", "midLevel": "Mid-level (2-5 years)", "seniorLevel": "Senior (5+ years)", "leadLevel": "Lead/Principal (8+ years)", "interviewTypeLabel": "Interview Type", "selectInterviewTypePlaceholder": "Select interview type", "technicalInterview": "Technical Interview", "behavioralInterview": "Behavioral Interview", "systemDesign": "System Design", "mixedInterview": "Mixed Interview", "technologyStackTitle": "Technology Stack", "techStackPlaceholder": "e.g. <PERSON>, Node.js, TypeScript", "addButton": "Add", "noTechnologiesAdded": "No technologies added yet", "addTechnologiesHint": "Add relevant technologies for this role", "aiQuestionGenerationTitle": "AI Question Generation", "aiQuestionGenerationDescription": "Configure how many questions the AI should generate for this interview", "numberOfQuestionsLabel": "Number of Questions", "numberOfQuestionsPlaceholder": "10", "questionCountHint": "Choose between 1-50 questions", "recommendedRangeLabel": "Recommended Range", "selectInterviewTypeFirst": "Select interview type first", "basedOnInterviewType": "Based on selected interview type", "questionsGeneratedTitle": "{questionCount, plural, one {1 Question} other {{questionCount} Questions}} Will Be Generated", "questionsGeneratedDescription": "AI will create {questionCount} relevant {type, select, technical {technical} behavioral {behavioral} system-design {system design} mixed {mixed} other {interview}} questions based on your template", "missingInformationToastTitle": "Missing Information", "missingInformationToastDescription": "Please fill in all required fields before saving.", "templateCreatedToastTitle": "Template Created", "templateCreatedToastDescription": "Your interview template has been created with {questionCount} AI-generated questions.", "creationFailedToastTitle": "Creation Failed", "creationFailedToastDescription": "Something went wrong. Please try again.", "completeRequiredFieldsTitle": "Complete Required Fields", "roleRequired": "Role title is required", "levelRequired": "Experience level is required", "typeRequired": "Interview type is required", "techStackRequired": "At least one technology is required", "questionCountRange": "Question count must be between 1-50", "cancelButton": "Cancel", "createTemplateButton": "Create Template", "creatingButton": "Creating..."}, "SearchPage": {"title": "Passisto Search", "titleHighlight": "<PERSON><PERSON>", "searchErrorTitle": "Search Error", "searchErrorDescription": "Failed to retrieve search results. Please try again.", "searchNowButton": "Search Now", "searchingButton": "Searching", "noSearchQueryToastTitle": "Empty Search", "noSearchQueryToastDescription": "Please enter a search query before searching.", "related-searches": "Related searches:", "search-anything": "Search anything...", "searching-in": "Searching in:"}, "integrationPage": {"loadingIntegration": "Loading integration ...", "integrationNotFoundTitle": "Integration not found", "integrationNotFoundDescription": "The requested integration does not exist or has been removed.", "backToIntegrations": "Back to Integrations", "integrationNameTitle": "{integrationName}", "integrationDescription": "View the configuration and status of this integration.", "overviewTitle": "Integration Overview", "permissionDeniedToastTitle": "Permission denied", "permissionDeniedToastDescription": "You don't have permission to view integrations."}, "editIntegrationPage": {"permissionDeniedToastTitle": "Permission denied", "permissionDeniedToastDescription": "You don't have permission to edit this integration.", "loadingIntegration": "Loading integration ...", "integrationNotFound": "Integration not found.", "backToIntegrationDetails": "Back to Integration Details", "editIntegrationTitle": "Edit Integration", "updateDescription": "Update the configuration for {integrationName}.", "integrationDetailsTitle": "Integration Details", "modifySettingsDescription": "Modify the fields below to update your integration settings."}, "newIntegrationPage": {"backToIntegrations": "Back to Integrations", "createIntegrationTitle": "Create New Integration", "createIntegrationDescription": "Connect your data sources to automate data synchronization.", "integrationDetailsTitle": "Integration Details", "fillFormDescription": "Fill out the form below to configure your integration."}, "DashboardPage": {"quick-actions": "Quick Actions", "ai-interview": "AI Interview", "email-builder": "Email Builder", "form-builder": "Form Builder", "total-teams": "Total Teams", "active-teams": "active teams", "total-users": "Total Users", "active-users": "active users", "team-activity-rate": "Team Activity Rate", "of-teams-have-members": "of teams have members", "avg-users-team": "Avg. Users per Team", "users-per-team": "users per team", "interviews": "Interviews", "completed": "completed", "pending": "pending", "emails": "Emails", "sent": "sent", "opened": "opened", "forms": "Forms", "submissions": "submissions", "conv-rate": "% conversion rate", "user-growth": "User Growth", "users": "Users", "team-status": "Team Status", "active": "Active", "inactive": "Inactive", "jan": "Jan", "feb": "Feb", "mar": "Mar", "apr": "Apr", "may": "May", "jun": "Jun", "jul": "Jul", "aug": "Aug", "sep": "Sep", "oct": "Oct", "nov": "Nov", "dec": "Dec", "interview-completed": "{count} completed", "interview-pending": "{count} pending", "email-sent": "{count} sent", "email-opened": "{count} opened", "form-submissions": "{count} submissions"}, "TeamsPage": {"permissionDeniedToastTitle": "Permission denied", "permissionDeniedToastDescription": "You don't have permission to access the teams page.", "loadingTeams": "Loading teams..", "errorLoadingTeamsTitle": "Error loading teams", "errorLoadingTeamsDescription": "There was an error loading your teams data.", "teamsTitle": "Teams", "teamsDescription": "Manage your organization's teams.", "createTeamButton": "Create Team", "searchTeamsPlaceholder": "Search teams...", "noTeamsFoundTitle": "No teams found.", "noTeamsFoundDescription": "Create your first team to get started.", "deleteTeamDialogTitle": "Delete Team", "deleteTeamDialogDescription": "Are you sure you want to delete {teamName}? This action cannot be undone and will remove all team associations.", "deleteCompanyTeamWarning": "The default 'Company' team cannot be deleted.", "cancelButton": "Cancel", "deleteButton": "Delete", "deletingButton": "Deleting...", "teamDeletedToastTitle": "Team deleted", "teamDeletedToastDescription": "{team<PERSON><PERSON>} has been deleted successfully.", "errorDeletingTeamTitle": "Error deleting team", "errorDeletingTeamDescription": "Failed to delete the team.", "authenticationRequiredToast": "Authentication required"}, "NewTeamPage": {"permissionDeniedToastTitle": "Permission denied", "permissionDeniedToastDescription": "You don't have permission to create teams.", "backToTeams": "Back to Teams", "breadcrumbTeams": "Teams", "breadcrumbCreateNewTeam": "Create New Team", "cardTitle": "Create New Team", "cardDescription": "Create a new team and assign permissions.", "formErrorsTitleTeamExists": "The team already exists", "formErrorsTitleFillFields": "Please fill in the following fields:", "formErrorsGenericTitle": "Error", "teamNameRequired": "Team name is required.", "descriptionRequired": "Description is required.", "teamAlreadyExists": "A team with the name \"{team<PERSON>ame}\" already exists.", "authenticationRequiredToast": "Authentication required", "teamCreatedToastTitle": "Team created", "teamCreatedToastDescription": "{teamName} has been created successfully.", "creationFailedToastTitle": "Creation failed", "creationFailedToastDescription": "An error occurred during creation.", "teamNameLabel": "Team Name", "teamNamePlaceholder": "Engineering", "descriptionLabel": "Description", "descriptionPlaceholder": "A brief description of the team's purpose and responsibilities", "teamPermissionsTitle": "Team Permissions", "permissionsSelected": "{count} permission{count, plural, one {} other {s}} selected", "permissionsDescription": "Permissions assigned to this team will be granted to all team members, unless explicitly revoked for specific users.", "loadingPermissions": "Loading permissions...", "failedToLoadPermissions": "Failed to load permissions", "cancelButton": "Cancel", "createTeamButton": "Create Team", "creatingTeamButton": "Creating..."}, "RolesPermissionsPage": {"loadingPermissions": "Loading Permissions...", "errorLoadingData": "Error Loading Data", "errorLoadingDataDescription": "Failed to load roles and permissions.", "pageTitle": "Roles & Permissions", "pageDescription": "Manage roles and permissions for your organization.", "infoBoxTitle": "Direct Permission Assignment", "infoBoxDescription1": "In this system, permissions are assigned directly to users, not inherited through roles. Roles are used for organizational purposes only. To assign permissions to a user, edit the user directly from the Users page.", "infoBoxDescription2": "On this page, you can view all available roles and permissions. Use the tabs to switch between views, the search box to find specific items, and the category filter to organize permissions by type.", "permissionSystemInfoButton": "Permission Management System", "rolesTab": "Roles", "permissionsTab": "Permissions", "rolesManagementTitle": "Roles Management", "rolesManagementDescription": "View and filter roles in your system", "searchRolesPlaceholder": "Search roles...", "roleNameTableHead": "Role Name", "systemNameTableHead": "System Name", "noRolesFound": "No roles found", "roleAdministrator": "Administrator", "roleManager": "Manager", "roleMember": "Member", "roleGuest": "Guest", "permissionsManagementTitle": "Permissions Management", "permissionsManagementDescription": "View and filter permissions in your system", "searchPermissionsPlaceholder": "Search permissions...", "allCategoriesOption": "All Categories", "permissionNameTableHead": "Permission Name", "categoryTableHead": "Category", "noPermissionsFound": "No permissions found", "uncategorized": "Uncategorized"}, "EditorPage": {"permissionDeniedToastTitle": "Permission denied", "permissionDeniedToastDescription": "You don't have permission to access the email builder page.", "loadingEditor": "Loading editor...", "backToGenerator": "Back to Generator"}, "SecurityInfoPage": {"security-system": "Security System", "understanding-the-permission-model-and-access-control": "Understanding the permission model and access control.", "back-to-roles-and-permissions": "Back to Roles & Permissions", "overview-0": "Overview", "roles": "Roles", "teams": "Teams", "scopes": "<PERSON><PERSON><PERSON>", "best-practices": "Best Practices", "permission-model-overview": "Permission Model Overview", "understanding-how-permissions-work-in-our-enterprise-system": "Understanding how permissions work in our enterprise system.", "multi-layered-permission-system": "Multi-Layered Permission System", "our-security-system-uses-a-multi-layered-approach-to-permissions-allowing-for-fine-grained-access-control-while-maintaining-flexibility-and-ease-of-management": "Our security system uses a multi-layered approach to permissions, allowing for fine-grained access control while maintaining flexibility and ease of management.", "users-are-assigned-roles-that-grant-a-set-of-permissions-roles-provide-a-baseline-of-access-appropriate-for-different-job-functions": "Users are assigned roles that grant a set of permissions. Roles provide a baseline of access appropriate for different job functions.", "team-based": "Team-based", "users-belong-to-teams-that-grant-additional-permissions-team-permissions-are-typically-scoped-to-the-teams-resources": "Users belong to teams that grant additional permissions. Team permissions are typically scoped to the team's resources.", "user-overrides": "User Overrides", "individual-users-can-be-granted-extra-permissions-or-have-inherited-permissions-revoked-to-handle-exceptions": "Individual users can be granted extra permissions or have inherited permissions revoked to handle exceptions.", "permission-inheritance-flow": "Permission Inheritance Flow", "role-permissions": "Role Permissions", "team-permissions": "Team Permissions", "combined-permissions": "Combined Permissions", "extra-permissions": "Extra Permissions", "revoked-permissions": "Revoked Permissions", "effective-permissions": "Effective Permissions", "permission-resolution": "Permission Resolution", "when-determining-if-a-user-has-a-specific-permission-the-system": "When determining if a user has a specific permission, the system:", "collects-all-permissions-from-the-users-roles": "Collects all permissions from the user's roles.", "adds-all-permissions-from-the-users-teams": "Adds all permissions from the user's teams.", "adds-any-extra-permissions-granted-directly-to-the-user": "Adds any extra permissions granted directly to the user.", "removes-any-permissions-explicitly-revoked-for-the-user": "Removes any permissions explicitly revoked for the user.", "considers-the-scope-of-each-permission-global-team-project-self": "Considers the scope of each permission (Global, Team, Project, Self).", "role-based-access-control": "Role-Based Access Control", "how-roles-provide-a-foundation-for-permissions-in-the-system": "How roles provide a foundation for permissions in the system.", "what-are-roles": "What Are Roles?", "roles-are-predefined-sets-of-permissions-that-represent-common-job-functions-or-responsibility-levels-within-your-organization-each-user-can-be-assigned-one-or-more-roles": "Roles are predefined sets of permissions that represent common job functions or responsibility levels within your organization. Each user can be assigned one or more roles.", "role-structure": "Role Structure", "each-role-contains-a-collection-of-permissions-with-specific-scopes-for-example": "Each role contains a collection of permissions with specific scopes. For example:", "administrator-role": "Administrator Role", "create-user": "Create User", "global": "Global", "manage-billing": "Manage Billing", "global-0": "Global", "view-analytics": "View Analytics", "manager-role": "Manager Role", "assign-user-to-team": "Assign User to Team", "team": "Team", "benefits-of-role-based-access": "Benefits of Role-Based Access", "simplified-management": "Simplified Management", "instead-of-assigning-individual-permissions-to-each-user-you-can-assign-roles-that-contain-predefined-sets-of-permissions-making-user-management-more-efficient": "Instead of assigning individual permissions to each user, you can assign roles that contain predefined sets of permissions, making user management more efficient.", "standardization": "Standardization", "roles-ensure-that-users-with-similar-responsibilities-have-consistent-access-rights-reducing-the-risk-of-permission-inconsistencies": "Roles ensure that users with similar responsibilities have consistent access rights, reducing the risk of permission inconsistencies.", "scalability": "Scalability", "as-your-organization-grows-roles-make-it-easier-to-onboard-new-users-with-the-appropriate-access-levels-without-having-to-configure-permissions-individually": "As your organization grows, roles make it easier to onboard new users with the appropriate access levels without having to configure permissions individually.", "auditability": "Auditability", "roles-provide-a-clear-structure-for-access-rights-making-it-easier-to-audit-who-has-access-to-what-and-why-they-have-that-access": "Roles provide a clear structure for access rights, making it easier to audit who has access to what and why they have that access.", "important-note": "Important Note", "roles-are-defined-at-the-system-level-and-cannot-be-created-or-modified-by-regular-users-contact-your-system-administrator-if-you-need-a-new-role-or-modifications-to-existing-roles": "Roles are defined at the system level and cannot be created or modified by regular users. Contact your system administrator if you need a new role or modifications to existing roles.", "team-based-permissions": "Team-Based Permissions", "how-teams-provide-contextual-access-to-resources": "How teams provide contextual access to resources.", "teams-and-permissions": "Teams and Permissions", "teams-or-groups-allow-you-to-organize-users-and-grant-permissions-based-on-the-resources-they-need-to-access-teams-are-particularly-useful-for-departmental-or-project-based-access-control": "Teams (or groups) allow you to organize users and grant permissions based on the resources they need to access. Teams are particularly useful for departmental or project-based access control.", "team-structure": "Team Structure", "team-members": "Team Members", "users-who-belong-to-the-team-and-inherit-its-permissions": "Users who belong to the team and inherit its permissions.", "team-permissions-0": "Team Permissions", "permissions-granted-to-all-members-of-the-team-typically-scoped-to-team-resources": "Permissions granted to all members of the team, typically scoped to team resources.", "resource-scope": "Resource Scope", "the-specific-resources-projects-data-etc-that-the-team-has-access-to": "The specific resources (projects, data, etc.) that the team has access to.", "engineering-team": "Engineering Team", "team-members-0": "Team Members", "john-doe": "<PERSON>", "jane-smith": "<PERSON>", "robert-johnson": "<PERSON>", "team-permissions-1": "Team Permissions", "create-data-provider": "Create Data Provider", "team-0": "Team", "view-analytics-0": "View Analytics", "team-1": "Team", "team-permission-scopes": "Team Permission Scopes", "team-permissions-are-typically-scoped-to-the-teams-resources-but-they-can-also-have-different-scope-types": "Team permissions are typically scoped to the team's resources, but they can also have different scope types.", "team-scope": "Team Scope", "permissions-apply-only-to-resources-owned-by-the-team-this-is-the-most-common-scope-for-team-permissions": "Permissions apply only to resources owned by the team. This is the most common scope for team permissions.", "global-scope": "Global Scope", "some-team-permissions-may-have-global-scope-allowing-team-members-to-perform-actions-across-the-entire-system": "Some team permissions may have Global scope, allowing team members to perform actions across the entire system.", "project-scope": "Project Scope", "permissions-may-be-limited-to-specific-projects-that-the-team-is-working-on-even-if-those-projects-are-shared-with-other-teams": "Permissions may be limited to specific projects that the team is working on, even if those projects are shared with other teams.", "team-vs-role-permissions": "Team vs. Role Permissions", "while-roles-define-what-a-user-can-do-based-on-their-job-function-teams-define-what-resources-they-can-access-this-combination-provides-a-flexible-and-powerful-access-control-system": "While roles define what a user can do based on their job function, teams define what resources they can access. This combination provides a flexible and powerful access control system.", "a-user-with-the-manager-role-in-the-engineering-team-can-manage-engineering-resources": "A user with the Manager role in the Engineering team can manage engineering resources.", "the-same-user-in-the-marketing-team-can-manage-marketing-resources": "The same user in the Marketing team can manage marketing resources.", "different-teams-can-have-different-permission-sets-based-on-their-needs": "Different teams can have different permission sets based on their needs.", "permission-scopes": "Permission Scopes", "understanding-how-permission-scopes-limit-the-reach-of-permissions": "Understanding how permission scopes limit the reach of permissions.", "what-are-permission-scopes": "What Are Permission Scopes?", "permission-scopes-define-the-boundaries-within-which-a-permission-applies-they-allow-for-fine-grained-control-over-what-resources-a-user-can-access-with-a-given-permission": "Permission scopes define the boundaries within which a permission applies. They allow for fine-grained control over what resources a user can access with a given permission.", "scope-types": "Scope Types", "permission-applies-across-the-entire-system-to-all-resources-of-the-relevant-type": "Permission applies across the entire system to all resources of the relevant type.", "permission-applies-only-to-resources-owned-by-or-associated-with-the-users-team-s": "Permission applies only to resources owned by or associated with the user's team(s).", "permission-applies-only-to-specific-projects-regardless-of-team-ownership": "Permission applies only to specific projects, regardless of team ownership.", "permission-applies-only-to-resources-created-by-or-directly-assigned-to-the-user": "Permission applies only to resources created by or directly assigned to the user.", "scope-examples": "Scope Examples", "view-analytics-with-global-scope": "View Analytics (with <PERSON> Scope)", "user-can-view-analytics-for-all-teams-and-projects-in-the-system": "User can view analytics for all teams and projects in the system.", "create-data-provider-with-team-scope": "Create Data Provider (with <PERSON>ope)", "user-can-create-data-providers-only-for-their-team-s": "User can create data providers only for their team(s).", "export-data-with-project-scope": "Export Data (with <PERSON> Scope)", "user-can-export-data-only-from-specific-projects-they-have-access-to": "User can export data only from specific projects they have access to.", "manage-profile-with-self-scope": "Manage Profile (with <PERSON>)", "user-can-only-manage-their-own-profile-information": "User can only manage their own profile information.", "scope-resolution": "Scope Resolution", "when-a-user-has-the-same-permission-with-different-scopes-e-g-from-different-roles-or-teams-the-system-uses-the-broadest-scope": "When a user has the same permission with different scopes (e.g., from different roles or teams), the system uses the broadest scope.", "scope-hierarchy-broadest-to-narrowest": "Scope Hierarchy (Broadest to Narrowest)", "key-0": "→", "example-scope-resolution": "Example: Scope Resolution", "if-a-user-has-the-view-analytics-permission-from": "If a user has the 'View Analytics' permission from:", "their-manager-role-with-team-scope": "Their Manager role (with <PERSON>)", "the-leadership-team-with-global-scope": "The Leadership Team (with <PERSON> Scope)", "the-effective-scope-will-be-global-as-its-broader-than-team": "The effective scope will be Global, as it's broader than Team.", "must-avoid": "Must avoid!"}, "UserCardView": {"noUsersFoundTitle": "No users found", "noUsersFoundDescription": "Try adjusting your search or filters", "actionsSrOnly": "Actions", "statusActive": "Active", "statusInactive": "Inactive", "inactiveAccount": "Inactive account", "viewDetails": "View Details", "edit": "Edit", "ban": "Ban", "activate": "Activate", "delete": "Delete", "emailLabel": "Email", "createdLabel": "Created", "rolesLabel": "Roles", "noRolesAssigned": "No roles assigned", "permissionsLabel": "Permissions", "permissionsCount": "{count} permission{count, plural, one {} other {s}} assigned", "noPermissions": "No permissions", "teamsLabel": "Teams", "noTeams": "No teams"}, "CandidateInterviewPage": {"permissionDeniedToastTitle": "Permission denied", "permissionDeniedToastDescription": "You don't have permission to access the interviews page.", "loadingInterviewDetails": "Loading interview details...", "interviewNotFoundTitle": "Interview Not Found", "interviewNotFoundDescription": "We couldn't find the interview you're looking for. It may have been deleted or doesn't exist.", "returnToInterviews": "Return to Interviews", "candidateInterviewTitle": "Candidate Interview", "viewFeedbackButton": "View Feedback", "roleCardTitle": "Role", "interviewTypeCardTitle": "Interview Type", "levelBadge": "Level", "statusCompleted": "Completed", "statusInProgress": "In Progress", "scoreCardTitle": "Score", "scoreNotScoredYet": "Not scored yet", "scoreExcellent": "Excellent", "scoreGood": "Good", "scoreNeedsImprovement": "Needs Improvement", "transcriptTitle": "Interview Transcript", "transcriptDescription": "Complete record of the conversation", "aiInterviewer": "AI Interviewer", "candidateLabel": "Candidate"}, "UserTableView": {"nameTableHead": "Name", "emailTableHead": "Email", "rolesTableHead": "Roles", "teamsTableHead": "Teams", "actionsTableHead": "Actions", "noUsersFound": "No users found.", "noRoles": "No roles", "noTeams": "No teams", "viewDetailsButtonTitle": "View Details", "viewDetailsSrOnly": "View details", "editUserButtonTitle": "Edit User", "editUserSrOnly": "Edit", "moreOptionsSrOnly": "More options", "banAction": "Ban", "activateAction": "Activate", "deleteAction": "Delete"}, "InterviewDetailPage": {"loadingInterviewDetails": "Loading interview details...", "interviewOrCandidateNotFoundTitle": "Interview or Candidate Not Found", "interviewOrCandidateNotFoundDescription": "We couldn't find the interview or candidate you're looking for. It may have been deleted or doesn't exist.", "returnToInterviews": "Return to Interviews", "interviewDetailsTitle": "Interview Details", "roleCardTitle": "Role", "interviewTypeCardTitle": "Interview Type", "completedBadge": "Completed", "notStartedBadge": "Not Started", "techStackCardTitle": "Tech Stack", "candidateInformationCardTitle": "Candidate Information", "statusLabel": "Status", "statusCompleted": "Completed", "statusInProgress": "In Progress", "statusPending": "Pending", "scoreLabel": "Score", "completedAtLabel": "Completed At", "feedbackStatusLabel": "Feedback Status", "feedbackAvailable": "Feedback Available", "noFeedback": "No Feedback", "interviewCompletedCardTitle": "Interview Completed", "interviewCompletedDescription": "This interview has been completed. Feedback has been provided to the company. You can view your interview transcript below.", "feedbackOnlyVisibleToCompany": "Feedback is only visible to the company", "readyToStartInterviewTitle": "Ready to start your interview?", "readyToStartInterviewDescription": "You're about to start a {interviewType} interview for the {interviewRole} {interviewLevel} position. Click the call button when you're ready.", "startInterviewButton": "Start Interview", "viewFullTranscriptButton": "View Full Transcript", "failedToStartInterviewToast": "Failed to start interview. Please try again.", "loadingInterview": "Loading interview...", "webcamErrorTitle": "Webcam Error", "webcamErrorDescription": "Could not access your webcam. Please check permissions.", "callStartedToastTitle": "Call Started", "callStartedToastDescription": "You are now connected with the AI Interviewer.", "interviewNotFoundTitle": "Interview Not Found", "interviewNotFoundDescription": "We couldn't find the interview you're looking for. It may have been deleted or doesn't exist.", "startButton": "Start"}, "must-avoid": "Must Avoid!", "ChatSettings": {"ariaLabelSettings": "Settings", "dialogTitle": "<PERSON><PERSON>", "dialogDescription": "Configure your chat experience", "knowledgeBasesLabel": "Knowledge Bases", "knowledgeBasesDescription": "Select which knowledge bases to search when asking questions", "errorLoadingKnowledgeBases": "Error loading knowledge bases: {error}", "allTab": "All", "ftpTab": "FTP", "jiraTab": "<PERSON><PERSON>", "webTab": "Web", "loadingKnowledgeBases": "Loading knowledge bases...", "knowledge": "Knowledge:"}, "Chatbot": {"welcomeMessage": "Hello! How can I help you today?", "errors": {"loadSessions": "Failed to load chat sessions. Please try again.", "connectionRefused": "Cannot connect to the chatbot server. Please check if the server is running on port 5921.", "corsError": "CORS error: The chatbot server is not configured to accept requests from this origin.", "noSessionsFound": "No chat sessions found for this user.", "serverError": "Server error: {message}", "loadHistory": "Failed to load chat history. Please try again.", "newSessionWelcome": "New session detected, showing welcome message", "userNotFound": "User information not available. Please try again in a moment.", "sendMessageFailed": "Failed to send message. Please try again.", "apiEndpointNotFound": "Chatbot API endpoint not found. Please check the API URL configuration.", "authenticationError": "Authentication error. You may not have permission to access the chatbot.", "createSessionFailed": "Failed to create a new chat session. Please try again.", "deleteSessionFailed": "Failed to delete chat session. Please try again."}, "logs": {"waitingForUser": "Waiting for backendUser to be available before fetching user sessions", "waitingForUserHistory": "Waiting for backend<PERSON>ser to be available before fetching chat history", "newSessionCreated": "Created new chat session with ID: {sessionId}", "newSessionDetected": "New session detected, showing welcome message"}}, "UserCreation": {"cardTitle": "Create New User", "cardDescription": "Add a new user with roles, permissions, and team assignments.", "formErrors": {"userExists": "The user already exists.", "fillInFields": "Please fill in the following fields:", "error": "Error", "firstNameRequired": "First name is required.", "lastNameRequired": "Last name is required.", "emailRequired": "Email is required.", "emailInvalid": "Please enter a valid email address.", "roleRequired": "At least one role must be selected.", "emailConflict": "A user with this email already exists."}, "firstNameLabel": "First Name", "firstNamePlaceholder": "<PERSON>", "lastNameLabel": "Last Name", "lastNamePlaceholder": "<PERSON><PERSON>", "emailLabel": "Email", "emailPlaceholder": "<EMAIL>", "tabs": {"roles": "Roles", "teams": "Teams", "permissions": "Permission Overrides"}, "rolesSection": {"label": "User Roles", "loading": "Loading roles...", "noRoles": "No roles available"}, "teamsSection": {"label": "Teams", "loading": "Loading teams...", "failedToLoad": "Failed to load teams: {error}", "noTeams": "No teams available", "companyTeamRequired": " (Required)", "memberCount": "{count, plural, one {member} other {members}}"}, "inheritedPermissions": {"title": "Inherited Permissions", "description": "These permissions are automatically granted through the user's roles and teams.", "noPermissions": "No inherited permissions. Assign roles or teams first.", "sourceRole": "Role: {name}", "sourceTeam": "Team: {name}", "revokeAction": "Revoke"}, "extraPermissions": {"title": "Extra Permissions", "description": "Grant additional permissions beyond what the user's roles and teams provide.", "loading": "Loading permissions...", "noPermissions": "No extra permissions selected."}, "submitButton": "Create User", "cancelButton": "Cancel", "toasts": {"permissionDeniedTitle": "Permission denied", "permissionDeniedDescription": "You don't have permission to create users.", "dataError": "Error loading data", "authRequired": "Authentication required", "userCreatedSuccess": "User created successfully", "creationFailedTitle": "Creation failed", "creationFailedDescription": "An error occurred during creation."}}, "EditUserPage": {"title": "Edit User", "description": "Update user details, roles, permissions, and teams.", "permissionDenied": {"title": "Permission denied", "description": "You don't have permission to update users."}, "errors": {"loadData": "Error loading data", "saveFailed": "Failed to update user", "teamsLoadFailed": "Failed to load teams"}, "successMessage": "User updated successfully", "formLabels": {"firstName": "First Name", "lastName": "Last Name", "email": "Email"}, "placeholders": {"firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "email": "<EMAIL>"}, "formErrors": {"firstName": "First name is required.", "lastName": "Last name is required.", "emailRequired": "Email is required.", "emailInvalid": "Please enter a valid email address.", "emailExists": "A user with this email already exists.", "roles": "At least one role must be selected.", "fillFields": "Please fill in the following fields:", "userExists": "The user already exists"}, "tabs": {"roles": "Roles", "teams": "Teams", "permissions": "Permission Overrides"}, "roles": {"title": "User Roles"}, "teams": {"title": "Teams", "noTeams": "No teams available", "required": "Required", "member": "member", "members": "members"}, "permissions": {"inheritedTitle": "Inherited Permissions", "inheritedDescription": "These permissions are automatically granted through the user's roles and teams.", "noInherited": "No inherited permissions. Assign roles or teams first.", "sourceRole": "Role: ", "sourceTeam": "Team: ", "revoke": "Revoke", "extraTitle": "Extra Permissions", "extraDescription": "Grant additional permissions beyond what the user's roles and teams provide.", "noTeamsTitle": "No teams selected", "noTeamsDescription": "Please select teams in the Teams tab first.", "selectTeams": "Select Teams", "selectProjects": "Select Projects", "effectiveTitle": "Effective Permissions", "effectiveDescription": "These are the permissions this user will have after applying all roles, teams, and overrides.", "noEffective": "No effective permissions"}, "loading": {"roles": "Loading roles...", "teams": "Loading teams...", "permissions": "Loading permissions..."}, "buttons": {"cancel": "Cancel", "save": "Save Changes", "saving": "Saving..."}}, "UserViewPage": {"permissionDeniedToastTitle": "Permission denied", "permissionDeniedToastDescription": "You don't have permission to view users.", "loadingUserDetails": "Loading user details...", "authenticationErrorToastTitle": "Authentication error", "authenticationErrorToastDescription": "Please sign in to view user details.", "userNotFoundToastTitle": "User not found", "userNotFoundToastDescription": "The requested user could not be found.", "backButton": "Back", "editUserButton": "Edit User", "activeBadge": "Active", "inactiveBadge": "Inactive", "rolesHeading": "Roles", "noRolesAssigned": "No roles assigned", "teamsHeading": "Teams", "noTeamsAssigned": "No teams assigned", "accountDetailsHeading": "Account Details", "createdLabel": "Created", "permissionsLabel": "Permissions", "userDetailsCardTitle": "User Details", "userDetailsCardDescription": "Comprehensive information about this user", "permissionsTab": "Permissions", "permissionOverridesTab": "Permission Overrides", "activityTab": "Activity", "noPermissionsAssigned": "This user has no permissions", "otherCategory": "Other", "extraPermissionSource": "Extra Permission", "fromRoleSource": "From {roleName} role", "fromTeamSource": "From {teamName} team", "unknownSource": "Unknown source", "extraPermissionsHeading": "Extra Permissions", "noExtraPermissions": "No extra permissions assigned", "revokedPermissionsHeading": "Revoked Permissions", "noRevokedPermissions": "No revoked permissions", "activityNotEnabled": "Activity tracking is not enabled", "enableActivityTracking": "Enable activity tracking in settings", "userNotFoundTitle": "User Not Found", "userNotFoundDescription": "The requested user could not be found.", "backToUsersButton": "Back to Users"}}