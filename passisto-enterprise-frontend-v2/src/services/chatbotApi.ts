import axios from "@/config/axios";
import { 
  ASK_CHATBOT, GET_SESSION_BY_ID, GET_USER_SESSIONS, DELETE_SESSION, NEW_USER_SESSION
} from "@/utils/routes";
import { v4 as uuidv4 } from 'uuid';

// Define types for the chat messages and responses
export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  sources?: string[]; // Optional array of source references
}

export interface ChatSession {
  id: string;
  title: string;
  lastMessage: string;
  timestamp: Date;
}

interface ChatbotResponse {
  answer: string;
  sources: string[];
  session_id: string;
  user_id: string | null;
}

interface ChatHistoryResponse {
  history: {
    messages: Array<{
      type: string;
      data: {
        content: string;
        sources?: string[]; // Add sources to the history response
      };
    }>;
  };
  user_id: string | null;
}

interface UserSessionsResponse {
  sessions: Array<{
    session_id: string;
    created_at: string;
    last_message: string;
    message_count: number;
  }>;
  total: number;
  user_id: string;
}

export const chatbotApi = {
  // Send a message to the chatbot
  sendMessage: async (
    message: string,
    sessionId: string = "",
    userId: string | null = null,
    aliasId: string = "" // Will be provided by the component using ChatSettingsContext
  ): Promise<ChatbotResponse> => {
    try {
      const response = await axios.post(ASK_CHATBOT, {
        message,
        indices: aliasId,
        session_id: sessionId,
      });

      return response.data;
    } catch (error) {
      console.error('Error sending message to chatbot:', error);
      throw error;
    }
  },

  // Get chat history for a specific session
  getChatHistory: async (
    sessionId: string,
    userId: string | null = null
  ): Promise<ChatMessage[]> => {
    try {
      const response = await axios.get( GET_SESSION_BY_ID(sessionId) , {
        params: { user_id: userId }
      });

      const data: ChatHistoryResponse = response.data;

      // Convert the history format to our ChatMessage format
      return data.history.messages.map(msg => ({
        role: msg.type === 'human' ? 'user' : 'assistant',
        content: msg.data.content,
        sources: msg.type === 'ai' && msg.data.sources ? msg.data.sources : undefined
      }));
    } catch (error) {
      console.error('Error fetching chat history:', error);
      throw error;
    }
  },

  // Get all chat sessions for a user
  getUserSessions: async (
    userId: string
  ): Promise<ChatSession[]> => {
    try {
      const response = await axios.get( GET_USER_SESSIONS );

      const data: UserSessionsResponse = response.data;

      // Convert the sessions to our ChatSession format
      return data.sessions.map(session => {
        // Use the session_id as the ID
        const id = session.session_id;

        // Use the last_message directly from the response
        const lastMessage = session.last_message || 'New Chat';

        // Create a title from the last message
        // Take the first few words of the last message as the title
        const title = lastMessage.split(' ').slice(0, 5).join(' ') +
          (lastMessage.split(' ').length > 5 ? '...' : '');

        // Use created_at as the timestamp
        const timestamp = new Date(session.created_at);

        return {
          id,
          title,
          lastMessage,
          timestamp
        };
      });
    } catch (error) {
      console.error('Error fetching user sessions:', error);
      throw error;
    }
  },

  // Create a new chat session
  createNewSession: (): string => {
    // Generate a new UUID for the session
    return uuidv4();
  },

  // Delete a chat session
  deleteSession: async (
    sessionId: string,
    userId: string | null = null
  ): Promise<boolean> => {
    try {
      const response = await axios.delete( DELETE_SESSION(sessionId) , {
        params: { user_id: userId }
      });

      return response.status === 200;
    } catch (error) {
      console.error('Error deleting chat session:', error);
      throw error;
    }
  }
};
