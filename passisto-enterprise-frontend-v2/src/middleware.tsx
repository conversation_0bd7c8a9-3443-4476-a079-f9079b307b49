import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server'
import { NextRequest, NextResponse } from 'next/server'
import createIntlMiddleware from 'next-intl/middleware';
import { routing } from './i18n/routing';

const isProtectedRoute = createRouteMatcher([
  '/(.*)/enterprise(.*)',
  '/(.*)/auth/onboarding',
  '/(.*)/workflows/(.*)',
  '/enterprise(.*)',
  '/auth/onboarding',
  '/workflows/(.*)'
])

// Create the internationalization middleware
const intlMiddleware = createIntlMiddleware(routing);

// Custom middleware to handle WebSocket and API requests
function customMiddleware(req: NextRequest) {
  // For Socket.IO paths, just let them through
  if (req.nextUrl.pathname.startsWith('/socket.io') ||
      req.nextUrl.pathname.startsWith('/api/socket') ||
      req.nextUrl.pathname.startsWith('/api/')) {
    return NextResponse.next();
  }

  return null; // Continue to other middleware
}

export default clerkMiddleware(async (auth, req) => {
  // First check if it's a WebSocket or special API request
  const customResponse = customMiddleware(req);
  if (customResponse) return customResponse;

  // Handle internationalization for non-API routes
  const intlResponse = intlMiddleware(req);

  // If intl middleware wants to redirect, let it
  if (intlResponse && intlResponse.status !== 200) {
    return intlResponse;
  }

  // Otherwise, proceed with Clerk auth
  const { userId, redirectToSignIn } = await auth();
  console.log("userID: " + userId);

  if (!userId && isProtectedRoute(req)) {
    return redirectToSignIn({ returnBackUrl: req.url });
  }

  return intlResponse || NextResponse.next();
})

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    // Always run for API routes
    '/(api|trpc)(.*)',
    // Add Socket.IO routes
    '/api/:path*', '/socket.io/:path*',
  ],
};
