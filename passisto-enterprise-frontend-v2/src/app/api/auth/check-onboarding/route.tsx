import { NextRequest, NextResponse } from "next/server"
import { getAuth } from "@clerk/nextjs/server"

export async function POST(req: NextRequest) {
    const { userId } = getAuth(req)

    if (!userId) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    try {
        const body = await req.json()
        const { onboarding } = body

        if (!onboarding) {
            return NextResponse.json({ error: "Onboarding status is required" }, { status: 400 })
        }

        // For demo purposes, returning random true/false
        const hasCompletedOnboarding = true

        return NextResponse.json({
            userId,
            onboarding,
            hasCompletedOnboarding
        })

    } catch (error) {
        return NextResponse.json({ error: "Invalid request body" }, { status: 400 })
    }
}