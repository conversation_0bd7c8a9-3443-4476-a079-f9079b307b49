import { Inter, <PERSON>o_Mono } from "next/font/google";
import "./globals.css";
import { Clerk<PERSON>rovider } from "@clerk/nextjs";
import { Toaster } from "@/components/ui/sonner";
import { ReduxProvider } from "@/store/provider";
import { hasLocale, NextIntlClientProvider } from "next-intl";
import { notFound } from "next/navigation";
import { routing } from "@/i18n/routing";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-geist-sans",
});

const robotoMono = Roboto_Mono({
  subsets: ["latin"],
  variable: "--font-geist-mono",
});

export const metadata = {
  title: "Passisto Enterprise",
  description: "AI-Powered Enterprise Platform",
};

export default async function RootLayout({
  children,
  params
}: {
  children: React.ReactNode;
  params: Promise<{locale: string}>;
}) {
  // Ensure that the incoming `locale` is valid
  const {locale} = await params;
  if (!hasLocale(routing.locales, locale)) {
    notFound();
  }
  return (
    <html lang="en">
      <body className={`${inter.variable} ${robotoMono.variable}`}>
        <NextIntlClientProvider>
        <ClerkProvider>
          <ReduxProvider>
            <Toaster />
            {children}
          </ReduxProvider>
        </ClerkProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
