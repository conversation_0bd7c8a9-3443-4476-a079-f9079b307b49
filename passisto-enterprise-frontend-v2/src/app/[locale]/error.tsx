"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { AlertCircle, ArrowLeft, RefreshCcw } from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { useTranslations } from "next-intl"

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  const t = useTranslations()
  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-50 to-white">
      {/* Navigation Bar */}
      <nav className="border-b bg-white py-4 px-6 flex justify-between items-center sticky top-0 z-10 shadow-sm">
        <Link 
          href="/enterprise/dashboard" 
          className="hover:opacity-90 transition-opacity"
        >
          <Image
            src="/passisto_logo.png"
            alt="Passisto Logo"
            width={150}
            height={40}
            priority
          />
        </Link>
      </nav>

      {/* Error Content */}
      <div className="container mx-auto py-16 px-4">
        <Card className="max-w-2xl mx-auto">
          <CardContent className="pt-6 flex flex-col items-center">
            <div className="h-16 w-16 rounded-full bg-destructive/10 flex items-center justify-center mb-4">
              <AlertCircle className="h-8 w-8 text-destructive" />
            </div>
            <h2 className="text-2xl font-bold mb-2">{t('something-went-wrong-0')}</h2>
            <p className="text-center text-muted-foreground mb-6">
              {t('an-unexpected-error-occurred-please-try-again-or-return-to-the-dashboard')}
            </p>
            <div className="flex gap-4">
              <Link href="/enterprise/dashboard">
                <Button>
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  {t('return-to-dashboard')}
                </Button>
              </Link>
              <Button
                variant="outline"
                onClick={() => reset()}
              >
                <RefreshCcw className="mr-2 h-4 w-4" />
                {t('try-again-0')}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}