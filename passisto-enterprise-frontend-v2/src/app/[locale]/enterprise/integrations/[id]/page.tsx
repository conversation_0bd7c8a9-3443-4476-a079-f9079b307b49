"use client"

import { useEffect, useState } from "react"
import { IntegrationDetails } from "@/components/integrations/integration-details"
import { But<PERSON> } from "@/components/ui/button"
import { ChevronLeft, Loader2 } from "lucide-react"
import Link from "next/link"
import { useAuth } from "@clerk/nextjs";
import { ALL_INTEGRATIONS } from "@/utils/routes";
import axiosInstance from "@/config/axios"
import { useParams } from "next/navigation"
import { checkPermissions, integrationPermissions } from "@/utils/ACTION_PERMISSIONS"
import { useBackendUser } from "@/hooks/useBackendUser";
import { useRouter } from "next/navigation"
import { toast } from "sonner"
import { useTranslations } from "next-intl" // Import useTranslations

type Integration = {
  id: string
  name: string
  providerType: "jira" | "ftp" | "web"
  status: string
  updateTime: number
  createdBy: string
  createdAt: string
  updatedAt: string
  jira?: any
  web?: any
  ftp?: any
}

export default function IntegrationPage() {
  const t = useTranslations("integrationPage") // Initialize useTranslations hook
  const params = useParams()
  const [integration, setIntegration] = useState<Integration | null>(null)
  const [isLoading, setLoading] = useState(true)
  const { getToken } = useAuth();
  const { backendUser, loading: backendUserLoading } = useBackendUser();
  const router = useRouter()

  useEffect(() => {
    // Permission check
    if (!backendUserLoading && backendUser) {
      const permissions = backendUser?.permissions ?? [];
      const hasViewPermission = checkPermissions(permissions, [integrationPermissions.canView]);

      if (!hasViewPermission) {
        toast.error(t("permissionDeniedToastTitle"), {
          description: t("permissionDeniedToastDescription"),
        });
        router.push("/enterprise/dashboard"); // Redirect to a safe page
      }
    }
  }, [backendUser, backendUserLoading, router, t]); // Add t to dependencies

  useEffect(() => {
    const fetchIntegration = async () => {
      // Only fetch if user has permission or if permissions are still loading
      if (!backendUserLoading && backendUser && !checkPermissions(backendUser.permissions || [], [integrationPermissions.canView])) {
        // If user doesn't have permission after loading, don't fetch and remain loading or show error state
        setLoading(false);
        return;
      }

      try {
        const token = await getToken();
        const headers = {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
        const res = await axiosInstance.get(`${ALL_INTEGRATIONS}/integration/${params.id}`,headers)
        if (res.status !== 200) throw new Error("Failed to fetch integration")
        setIntegration(res.data)
      } catch (err) {
        console.error("Error loading integration:", err)
        // Optionally, show a more specific error if fetch fails
      } finally {
        setLoading(false)
      }
    }

    // Ensure backendUser is loaded before attempting to fetch or check permissions
    if (!backendUserLoading) {
      fetchIntegration()
    }
  }, [params.id, getToken, backendUser, backendUserLoading]); // Add getToken, backendUser, backendUserLoading to dependencies


  if (isLoading) {
    return (
      <div className="flex min-h-screen flex-col">
        <main className="flex-1 container py-6">
          <div className="flex items-center justify-center h-64">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            <p className="ml-4 text-muted-foreground">{t("loadingIntegration")}</p>
          </div>
        </main>
      </div>
    )
  }

  if (!integration) {
    return (
      <div className="flex min-h-screen flex-col">
        <main className="flex-1 container py-6">
          <div className="flex flex-col items-center justify-center h-64 text-center">
            <p className="text-xl font-semibold text-muted-foreground">{t("integrationNotFoundTitle")}</p>
            <p className="text-muted-foreground mt-2">{t("integrationNotFoundDescription")}</p>
            <Link href="/enterprise/integrations">
              <Button variant="ghost" size="sm" className="mt-4">
                <ChevronLeft className="mr-2 h-4 w-4" />
                {t("backToIntegrations")}
              </Button>
            </Link>
          </div>
        </main>
      </div>
    )
  }

  return (
    <div className="flex min-h-screen flex-col bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-950">
      <main className="flex-1 container py-10 px-4 md:px-8">
        {/* Header Section */}
        <div className="mb-8">
          <Link href="/enterprise/integrations">
            <Button
              variant="ghost"
              size="sm"
              className="mb-5 flex items-center text-blue-600 hover:text-blue-700 transition-colors dark:text-blue-400 dark:hover:text-blue-500"
            >
              <ChevronLeft className="mr-2 h-4 w-4" />
              {t("backToIntegrations")}
            </Button>
          </Link>

          <h1 className="text-4xl font-extrabold tracking-tight text-gray-900 dark:text-white">
            {t("integrationNameTitle", { integrationName: integration.name })}
          </h1>

          <p className="mt-3 text-lg text-gray-600 dark:text-gray-400">
            {t("integrationDescription")}
          </p>
        </div>

        {/* Details Section */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg px-8 py-10">
          <div className="flex items-center mb-6">
            <div className="bg-green-100 dark:bg-green-900 p-2 rounded-full">
              <svg
                className="h-6 w-6 text-green-600 dark:text-green-300"
                fill="none"
                stroke="currentColor"
                strokeWidth={2}
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" d="M15 12H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h2 className="ml-3 text-2xl font-semibold text-gray-800 dark:text-gray-100">
              {t("overviewTitle")}
            </h2>
          </div>

          <IntegrationDetails integration={integration} />
        </div>
      </main>
    </div>
  )
}