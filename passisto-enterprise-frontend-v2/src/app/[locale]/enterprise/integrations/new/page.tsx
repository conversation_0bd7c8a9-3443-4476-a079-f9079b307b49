"use client"

import { CreateIntegrationForm } from "@/components/integrations/create-integration-form"
import { But<PERSON> } from "@/components/ui/button"
import { ChevronLeft } from "lucide-react"
import Link from "next/link"
import { useTranslations } from "next-intl" // Import useTranslations

export default function NewIntegrationPage() {
  const t = useTranslations("newIntegrationPage") // Initialize useTranslations hook

  return (
    <div className="flex min-h-screen flex-col bg-gray-50 dark:bg-gray-900">
      <main className="flex-1 container py-6">
        {/* Header Section */}
        <div className="mb-6">
          <Link href="/enterprise/integrations">
            <Button
              variant="ghost"
              size="sm"
              className="mb-4 flex items-center text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-500"
            >
              <ChevronLeft className="mr-2 h-4 w-4" />
              {t("backToIntegrations")}
            </Button>
          </Link>
          <h1 className="text-4xl font-bold tracking-tight text-gray-800 dark:text-gray-100">
            {t("createIntegrationTitle")}
          </h1>
          <p className="text-muted-foreground mt-2 text-gray-600 dark:text-gray-400">
            {t("createIntegrationDescription")}
          </p>
        </div>

        {/* Form Section */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-100 mb-4">
            {t("integrationDetailsTitle")}
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
            {t("fillFormDescription")}
          </p>
          <CreateIntegrationForm />
        </div>
      </main>
    </div>
  )
}