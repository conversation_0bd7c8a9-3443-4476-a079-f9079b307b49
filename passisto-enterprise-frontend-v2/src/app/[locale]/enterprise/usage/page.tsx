"use client"

import { useEffect, useState } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Clock, Mail } from "lucide-react"
import { useAuth } from "@clerk/nextjs"
import axiosInstance from "@/config/axios"
import { COMPANY_USAGE_INFO } from "@/utils/routes"
import { toast } from "sonner"

interface UsageInfo {
  limit: number
  used: number
  remaining: number
}

interface CompanyUsageResponse {
  success: boolean
  emailBuilder: UsageInfo
  aiInterviewer: UsageInfo
}

export default function CompanyUsagePage() {
  const { getToken } = useAuth()
  const [usageData, setUsageData] = useState<CompanyUsageResponse | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchUsageData = async () => {
      try {
        const token = await getToken()
        const response = await axiosInstance.get<CompanyUsageResponse>(
          COMPANY_USAGE_INFO,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        )

        if (response.data.success) {
          setUsageData(response.data)
        }
      } catch (error) {
        console.error("Failed to fetch usage data:", error)
        toast.error("Failed to load usage information")
      } finally {
        setLoading(false)
      }
    }

    fetchUsageData()
  }, [getToken])

  if (loading) {
    return (
      <div className="container mx-auto p-8">
        <div className="animate-pulse">
          <div className="h-8 w-64 bg-gray-200 rounded mb-8"></div>
          <div className="space-y-6">
            <div className="h-48 bg-gray-200 rounded"></div>
            <div className="h-48 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-3xl font-bold mb-8">Company Usage Information</h1>

      <div className="grid gap-8">
        {/* AI Interviewer Usage */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-xl font-bold flex items-center gap-2">
              <Clock className="h-5 w-5" />
              AI Interviewer Usage
            </CardTitle>
            <span className={`px-3 py-1 rounded-full text-sm ${
              usageData?.aiInterviewer.remaining === 0 ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
            }`}>
              {usageData?.aiInterviewer.remaining === 0 ? 'Exceeded' : 'Active'}
            </span>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Total Limit</TableHead>
                  <TableHead>Used</TableHead>
                  <TableHead>Remaining</TableHead>
                  <TableHead>Usage Percentage</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell>{usageData?.aiInterviewer.limit} minutes</TableCell>
                  <TableCell>{usageData?.aiInterviewer.used} minutes</TableCell>
                  <TableCell>{usageData?.aiInterviewer.remaining} minutes</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div
                          className={`h-2.5 rounded-full ${
                            (usageData?.aiInterviewer.used || 0) / (usageData?.aiInterviewer.limit || 1) > 0.9
                              ? 'bg-red-500'
                              : 'bg-green-500'
                          }`}
                          style={{
                            width: `${((usageData?.aiInterviewer.used || 0) / (usageData?.aiInterviewer.limit || 1)) * 100}%`
                          }}
                        ></div>
                      </div>
                      <span className="text-sm">
                        {Math.round(((usageData?.aiInterviewer.used || 0) / (usageData?.aiInterviewer.limit || 1)) * 100)}%
                      </span>
                    </div>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Email Builder Usage */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-xl font-bold flex items-center gap-2">
              <Mail className="h-5 w-5" />
              Email Builder Usage
            </CardTitle>
            <span className={`px-3 py-1 rounded-full text-sm ${
              usageData?.emailBuilder.remaining === 0 ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
            }`}>
              {usageData?.emailBuilder.remaining === 0 ? 'Exceeded' : 'Active'}
            </span>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Total Limit</TableHead>
                  <TableHead>Used</TableHead>
                  <TableHead>Remaining</TableHead>
                  <TableHead>Usage Percentage</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell>{usageData?.emailBuilder.limit} emails</TableCell>
                  <TableCell>{usageData?.emailBuilder.used} emails</TableCell>
                  <TableCell>{usageData?.emailBuilder.remaining} emails</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div
                          className={`h-2.5 rounded-full ${
                            (usageData?.emailBuilder.used || 0) / (usageData?.emailBuilder.limit || 1) > 0.9
                              ? 'bg-red-500'
                              : 'bg-green-500'
                          }`}
                          style={{
                            width: `${((usageData?.emailBuilder.used || 0) / (usageData?.emailBuilder.limit || 1)) * 100}%`
                          }}
                        ></div>
                      </div>
                      <span className="text-sm">
                        {Math.round(((usageData?.emailBuilder.used || 0) / (usageData?.emailBuilder.limit || 1)) * 100)}%
                      </span>
                    </div>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}