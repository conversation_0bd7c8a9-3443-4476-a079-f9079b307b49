"use client"

import { useEffect, useState, useRef } from "react"
import { EmailBuilder, type EmailComponent } from "../../_components/email-builder"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"
import { EmailPreviewDialog } from "../../_components/email-preview-dialog"
import axiosInstance from "@/config/axios"
import { EMAIL_BUILDER_GET_BY_ID } from "@/utils/routes"
import { useAuth } from "@clerk/nextjs"
import { AxiosResponse } from "axios"
import { checkPermissions, emailBuilderPermissions } from "@/utils/ACTION_PERMISSIONS"
import { useBackendUser } from "@/hooks/useBackendUser"
import { useRouter } from "next/navigation"
import { toast } from "sonner"
import { useTranslations } from "next-intl" // Import useTranslations

interface GeneratEmailResponse {
  id: String;
  companyId: String;
  title: string;
  description: string;
  fields: [];
}

export default function EditorPage({ params }: { params: { id: string } }) {
  const { getToken } = useAuth()
  const { backendUser, loading: backendUserLoading } = useBackendUser()
  const router = useRouter()
  const t = useTranslations("EditorPage") // Initialize useTranslations

  const [initialComponents, setInitialComponents] = useState<EmailComponent[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [emailHTML, setEmailHTML] = useState<string>("")
  const emailBuilderRef = useRef<{ generateHTML: () => string } | null>(null)

  const getEmailById = async (emailId: String) => {
    try {
      console.log(emailId)
      const token = await getToken()

      const response: AxiosResponse<GeneratEmailResponse> = await axiosInstance.get(EMAIL_BUILDER_GET_BY_ID(emailId), {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
      console.log(response.data.fields)
      setInitialComponents(response.data.fields)
      // return response.data.email;
    } catch (error: any) {
      console.error("Error fetching email:", error?.response?.data || error?.message)
      // Potentially show a toast for failed fetch
      toast.error("Failed to load email", {
        description: "There was an error fetching the email template."
      })
      throw error
    }
  }

  useEffect(() => {
    // Only check permissions after backendUser has loaded
    if (!backendUserLoading && backendUser) {
      const permissions = backendUser?.permissions ?? []

      // Check if user has at least one email builder-related permission
      const hasAnyEmailBuilderPermission = checkPermissions(permissions, [
        emailBuilderPermissions.canViewTemplate,
        emailBuilderPermissions.canEditBuilder,
        emailBuilderPermissions.canDeleteBuilder,
        emailBuilderPermissions.canSendBuilderEmail,
      ])

      // Redirect if user doesn't have any email builder-related permissions
      if (!hasAnyEmailBuilderPermission) {
        toast.error(t("permissionDeniedToastTitle"), {
          description: t("permissionDeniedToastDescription"),
        })
        router.back()
      } else {
        // If user has permission, proceed to load email
        getEmailById(params.id)
          .then(() => setIsLoading(false))
          .catch(() => setIsLoading(false)) // Ensure loading state is set to false even on error
      }
    }
  }, [backendUser, backendUserLoading, router, params.id, t]) // Add 't' to dependencies

  // Function to get the HTML from the EmailBuilder component
  const getEmailHTML = (): string => {
    // Try to get fresh HTML from the ref if available
    if (emailBuilderRef.current) {
      return emailBuilderRef.current.generateHTML()
    }
    // Fall back to the stored HTML
    return emailHTML
  }

  // Function to update the HTML when it changes
  const handleHTMLUpdate = (html: string) => {
    setEmailHTML(html)
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-pulse">{t("loadingEditor")}</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="p-2 border-b bg-card flex items-center justify-between">
        <Link href="/enterprise/ai-agents/email-builder">
          <Button variant="outline" size="sm" className="gap-1">
            <ArrowLeft className="h-4 w-4" />
            {t("backToGenerator")}
          </Button>
        </Link>
        <EmailPreviewDialog generateHTML={getEmailHTML} />
      </div>
      <EmailBuilder initialComponents={initialComponents} onHTMLChange={handleHTMLUpdate} ref={emailBuilderRef} />
    </div>
  )
}