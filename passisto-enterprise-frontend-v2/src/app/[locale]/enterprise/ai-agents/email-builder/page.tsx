"use client";

import { AITemplateGenerator } from "./_components/ai-template-generator";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { LayoutTemplate } from "lucide-react";
import { useBackendUser } from "@/hooks/useBackendUser";
import { checkPermissions, emailBuilderPermissions } from "@/utils/ACTION_PERMISSIONS";
import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { useTranslations } from "next-intl"; // Import useTranslations

export default function EmailBuilder() {
  const { backendUser, loading: backendUserLoading } = useBackendUser();
  const router = useRouter();
  const t = useTranslations("emailBuilderPage"); // Initialize useTranslations

  useEffect(() => {
    // Only check permissions after backendUser has loaded
    if (!backendUserLoading && backendUser) {
      const permissions = backendUser?.permissions ?? [];

      // Check if user has at least one email builder-related permission
      const hasAnyEmailBuilderPermission = checkPermissions(permissions, [
        emailBuilderPermissions.canEnhanceDescription,
        emailBuilderPermissions.canGenerateTemplate,
        emailBuilderPermissions.canViewTemplate,
        emailBuilderPermissions.canEditBuilder,
        emailBuilderPermissions.canDeleteBuilder,
        emailBuilderPermissions.canSendBuilderEmail,
      ]);

      // Redirect if user doesn't have any email builder-related permissions
      if (!hasAnyEmailBuilderPermission) {
        toast.error(t("permissionDeniedToastTitle"), {
          description: t("permissionDeniedToastDescription"),
        });
        router.push("/enterprise/dashboard");
      }
    }
  }, [backendUser, backendUserLoading, router]); // Add 't' to the dependency array

  return (
    <div className="min-h-[calc(100vh-4rem)]  bg-background">
      <div className="container mx-auto py-4 px-4">
        <div className="flex justify-end mb-2">
          <Link href="email-builder/templates">
            {emailBuilderPermissions.canViewTemplate(
              backendUser?.permissions || []
            ) && (
              <Button variant="outline" className="gap-2">
                <LayoutTemplate className="h-4 w-4" />
                {t("viewTemplatesButton")}
              </Button>
            )}
          </Link>
        </div>
        <AITemplateGenerator />
      </div>
    </div>
  );
}