"use client"

import type React from "react"

import { useState } from "react"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import type { StoredForm, FormSubmissionData } from "../_lib/types"
import { cn } from "@/lib/utils"
import { CheckCircle2 } from "lucide-react"

type FormSubmissionProps = {
  form: StoredForm
}

export function FormSubmission({ form }: FormSubmissionProps) {
  const [formData, setFormData] = useState<Record<string, any>>({})
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [submitted, setSubmitted] = useState(false)

  const handleChange = (id: string, value: any) => {
    setFormData((prev) => ({ ...prev, [id]: value }))

    // Clear error when field is filled
    if (errors[id]) {
      setErrors((prev) => {
        const newErrors = { ...prev }
        delete newErrors[id]
        return newErrors
      })
    }
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    form.fields.forEach((field) => {
      // Check required fields
      if (field.required && (!formData[field.id] || formData[field.id] === "")) {
        newErrors[field.id] = "This field is required"
      }

      // Check email validation
      if (
        field.validation === "email" &&
        formData[field.id] &&
        !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData[field.id])
      ) {
        newErrors[field.id] = "Please enter a valid email address"
      }
    })

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (validateForm()) {
      // Save submission to localStorage
      const submission: FormSubmissionData = {
        id: `submission-${Date.now()}`,
        formId: form.id,
        data: formData,
        submittedAt: new Date().toISOString(),
      }

      // Get existing submissions or create empty array
      const existingSubmissions = localStorage.getItem("submissions")
      const submissions = existingSubmissions ? JSON.parse(existingSubmissions) : []

      // Add new submission
      submissions.push(submission)

      // Save back to localStorage
      localStorage.setItem("submissions", JSON.stringify(submissions))

      // Show success message
      setSubmitted(true)
    }
  }

  if (submitted) {
    return (
      <div className="min-h-screen flex flex-col">
        {/* Header */}
        <div className="w-full py-8" style={{ backgroundColor: form.brandColor || "#4f46e5" }} />

        <div className="flex-1 flex items-center justify-center p-8">
          <div className="bg-white rounded-lg shadow-lg max-w-md w-full p-8 text-center">
            <div className="mb-6 flex justify-center">
              <div className="rounded-full bg-green-100 p-3">
                <CheckCircle2 className="h-12 w-12 text-green-600" />
              </div>
            </div>
            <h2 className="text-2xl font-bold mb-2">Form Submitted Successfully!</h2>
            <p className="text-gray-600 mb-6">Thank you for your submission.</p>
            <Button
              onClick={() => setSubmitted(false)}
              style={{ backgroundColor: form.brandColor || "#4f46e5" }}
              className="w-full"
            >
              Submit Another Response
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex flex-col">
      {/* Header */}
      <div className="fixed top-0 left-0 w-full py-4 z-50" style={{ backgroundColor: form.brandColor || "#4f46e5" }} />

      <div className="max-w-3xl mx-auto w-full px-4 py-8 mt-2">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          {/* Form Header */}
          <div className="border-b">
            <div className="px-6 py-4">
              {form.logo && (
                <div className="mb-4 flex justify-center">
                  <img src={form.logo || "/placeholder.svg"} alt="Company Logo" className="h-16 object-contain" />
                </div>
              )}
              <h1 className="text-2xl font-bold mb-2" style={{ color: form.brandColor || "#4f46e5" }}>
                {form.title}
              </h1>
              {form.description && <p className="text-gray-600">{form.description}</p>}
            </div>
          </div>

          {/* Form Body */}
          <form onSubmit={handleSubmit} className="p-6">
            <div className="space-y-6">
              {form.fields.map((field) => (
                <div key={field.id} className="p-4 bg-gray-50 rounded-lg">
                  <div className="space-y-2">
                    <Label htmlFor={field.id} className="flex items-center gap-1 text-base">
                      {field.label}
                      {field.required && <span className="text-red-500">*</span>}
                    </Label>

                    {field.type === "text" && (
                      <Input
                        id={field.id}
                        type="text"
                        value={formData[field.id] || ""}
                        onChange={(e) => handleChange(field.id, e.target.value)}
                        className={cn("bg-white", errors[field.id] ? "border-red-500" : "")}
                      />
                    )}

                    {field.type === "textarea" && (
                      <Textarea
                        id={field.id}
                        value={formData[field.id] || ""}
                        onChange={(e) => handleChange(field.id, e.target.value)}
                        className={cn("bg-white min-h-[100px]", errors[field.id] ? "border-red-500" : "")}
                      />
                    )}

                    {field.type === "email" && (
                      <Input
                        id={field.id}
                        type="email"
                        value={formData[field.id] || ""}
                        onChange={(e) => handleChange(field.id, e.target.value)}
                        className={cn("bg-white", errors[field.id] ? "border-red-500" : "")}
                      />
                    )}

                    {field.type === "select" && (
                      <Select value={formData[field.id] || ""} onValueChange={(value) => handleChange(field.id, value)}>
                        <SelectTrigger className={cn("bg-white", errors[field.id] ? "border-red-500" : "")}>
                          <SelectValue placeholder="Select an option" />
                        </SelectTrigger>
                        <SelectContent>
                          {(field.options || []).map((option) => (
                            <SelectItem key={option} value={option}>
                              {option}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    )}

                    {field.type === "checkbox" && (
                      <div className="flex items-center space-x-2 pt-2">
                        <Checkbox
                          id={field.id}
                          checked={formData[field.id] || false}
                          onCheckedChange={(checked) => handleChange(field.id, checked)}
                        />
                        <label
                          htmlFor={field.id}
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          {field.checkboxLabel || "Yes"}
                        </label>
                      </div>
                    )}

                    {field.type === "radio" && (
                      <RadioGroup
                        value={formData[field.id] || ""}
                        onValueChange={(value) => handleChange(field.id, value)}
                      >
                        <div className="space-y-2 pt-2">
                          {(field.options || []).map((option) => (
                            <div key={option} className="flex items-center space-x-2">
                              <RadioGroupItem value={option} id={`${field.id}-${option}`} />
                              <Label htmlFor={`${field.id}-${option}`}>{option}</Label>
                            </div>
                          ))}
                        </div>
                      </RadioGroup>
                    )}

                    {field.type === "file" && (
                      <Input
                        id={field.id}
                        type="file"
                        onChange={(e) => {
                          const files = (e.target as HTMLInputElement).files
                          handleChange(field.id, files ? files[0] : null)
                        }}
                        className={cn("bg-white", errors[field.id] ? "border-red-500" : "")}
                      />
                    )}

                    {errors[field.id] && <p className="text-red-500 text-sm">{errors[field.id]}</p>}
                  </div>
                </div>
              ))}

              {form.fields.length > 0 && (
                <div className="flex justify-between items-center pt-4">
                  <p className="text-sm text-gray-500">* Required fields</p>
                  <Button
                    type="submit"
                    className="px-8"
                    style={{ backgroundColor: form.brandColor || "#4f46e5", borderColor: form.brandColor || "#4f46e5" }}
                  >
                    Submit
                  </Button>
                </div>
              )}

              {form.fields.length === 0 && (
                <div className="text-center py-8 border-2 border-dashed rounded-lg">
                  <p className="text-gray-500">This form has no fields.</p>
                </div>
              )}
            </div>
          </form>

          {/* Form Footer */}
          <div className="border-t px-6 py-4">
            <p className="text-xs text-gray-500 text-center">This form was created using AI-Powered Form Builder</p>
          </div>
        </div>
      </div>
    </div>
  )
}

