"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Textarea } from "@/components/ui/textarea";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Wand2, <PERSON><PERSON><PERSON>, Loader2, Check } from "lucide-react";
import type { FormTemplate, StoredForm } from "../_lib/types";
import { generateSlug } from "../_lib/utils";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ENHANCE_DESCRIPTION_PROMPT, GENERATE_TEMPLATE_PROMPT } from "../_data/PROMPTS";
import axiosInstance from "@/config/axios";
import { FORM_BUILDER_ENHANCE_DESCRIPTION, FORM_BUILDER_GENERATE_FORM } from "@/utils/routes";
import { useAuth } from "@clerk/nextjs";
import { <PERSON><PERSON> } from "next/font/google";
import { Console } from "console";
import { useTranslations } from "next-intl";


export function FormDescriptionInput() {
  const t  = useTranslations()
  const router = useRouter();
   const { getToken } = useAuth();
  const [formIntent, setFormIntent] = useState("");
  const [enhancedDescription, setEnhancedDescription] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [isEnhancing, setIsEnhancing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const enhanceDescription = async () => {
    if (!formIntent.trim()) {
      setError(t('please-enter-a-form-description-first'));
      return;
    }

    setError(null);
    setEnhancedDescription("");
    setIsEnhancing(true);

    try {
      const prompt = ENHANCE_DESCRIPTION_PROMPT(formIntent);

      
      const token = await getToken();
      
            const enhanced =
              await axiosInstance.post(FORM_BUILDER_ENHANCE_DESCRIPTION, {
                userInput: prompt,
              },
              {
                headers: {
                  Authorization: `Bearer ${token}`,
                },
              });
      // const response = await axiosInstance.post(FORM_BUILDER_ENHANCE_DESCRIPTION, {formIntent})
      const result =  enhanced.data.enhancedDescription
    console.log(result);
      setEnhancedDescription(result.trim());
      
    } catch (err) {
      console.error("Error enhancing description:", err);
      setError(t('failed-to-enhance-description-please-try-again'));
    } finally {
      setIsEnhancing(false);
    }
  };

  const useEnhancedDescription = () => {
    setFormIntent(enhancedDescription);
    setEnhancedDescription("");
    setSuccess(t('enhanced-description-applied'));

    // Clear success message after 3 seconds
    setTimeout(() => {
      setSuccess(null);
    }, 3000);
  };

  const generateForm = async () => {
    if (!formIntent.trim()) {
      setError(t('please-describe-your-form-purpose'));
      return;
    }

    setError(null);
    setIsGenerating(true);

    try {
      const prompt = GENERATE_TEMPLATE_PROMPT(formIntent);

      // const text = await generateWithOpenRouter(prompt);
      //const text = await "generateWithOpenRouter(prompt)";

      const token = await getToken();

      const text=
        await axiosInstance.post(FORM_BUILDER_GENERATE_FORM, {
          userInput: prompt,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

      // Parse the JSON response£
      console.log(text.data.formTemplate);
      //const jsonMatch = text.data.formTemplate.match(/\{[\s\S]*\}/);
      if (true) {
        // const parsedTemplate = JSON.parse(jsonMatch[0]) as FormTemplate;
        
        const parsedTemplate = text.data.formTemplate as FormTemplate;
        console.log(text.data.formTemplate);
        

        // Create a new form with unique ID and slug
        const formId = `form-${Date.now()}`;
        // const slug = generateSlug(parsedTemplate.title);

        const newForm: StoredForm = {
          id: formId,
          // slug,
          title: parsedTemplate.title,
          description: parsedTemplate.description,
          fields: parsedTemplate.fields.map((field) => ({
            ...field,
            id: `field-${Date.now()}-${Math.random()
              .toString(36)
              .substr(2, 9)}`,
          })),
          brandColor: "#4f46e5",
          logo: "",
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };

        // Save to localStorage
        const existingForms = localStorage.getItem("forms");
        const forms = existingForms ? JSON.parse(existingForms) : [];
        forms.push(newForm);
        localStorage.setItem("forms", JSON.stringify(forms));

        // Trigger storage event for other tabs
        window.dispatchEvent(new Event("storage"));

        // Navigate to editor
        router.push(`/enterprise/ai-agents/form-builder/editor/${formId}`);
      } else {
        setError("Failed to generate form. Please try again.");
      }
    } catch (err) {
      console.error("Error generating form:", err);
      setError(
        t('an-error-occurred-while-generating-the-form-please-try-again')
      );
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <Card className="shadow-lg">
      <CardHeader>
        <CardTitle>{t('describe-your-form')}</CardTitle>
        <CardDescription>
          {t('tell-us-what-kind-of-form-you-need-and-our-ai-will-generate-it-for-you')}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Textarea
            placeholder={t('describe-your-form-purpose-e-g-job-application-form-for-a-marketing-position-event-registration-form-for-a-conference')}
            value={formIntent}
            onChange={(e) => setFormIntent(e.target.value)}
            className="resize-none min-h-[150px]"
            rows={5}
          />
          {error && <p className="text-red-500 text-sm mt-2">{error}</p>}
          {success && (
            <Alert className="mt-2 bg-green-50 border-green-200">
              <AlertDescription className="flex items-center text-green-600">
                <Check className="h-4 w-4 mr-2" />
                {success}
              </AlertDescription>
            </Alert>
          )}

          <div className="bg-muted/50 p-4 rounded-md">
            <h3 className="text-sm font-medium mb-2">{t('prompt-tips')}</h3>
            <ul className="text-sm text-muted-foreground space-y-1 list-disc pl-4">
              <li>
                {t('specify-the-purpose-of-the-form-job-application-event-registration-feedback-etc')}
              </li>
              <li>{t('include-your-company-or-brand-name-for-customization')}</li>
              <li>
                {t('mention-required-fields-e-g-name-email-phone-number')}
              </li>
              <li>
                {t('describe-any-conditional-logic-e-g-show-extra-fields-based-on-selection')}
              </li>
              <li>
                {t('specify-form-actions-e-g-email-responses-save-to-database-integrate-with-apis')}
              </li>
              <li>
                {t('mention-if-you-need-file-uploads-e-g-resumes-images-documents')}
              </li>
              <li>
                {t('describe-validation-rules-e-g-required-fields-email-format-character-limits')}
              </li>
              <li>
                {t('indicate-if-you-want-branding-elements-like-logos-and-theme-colors')}
              </li>
              <li>
                {t('specify-if-you-need-auto-confirmation-emails-or-notifications')}
              </li>
            </ul>
          </div>
        </div>

        {enhancedDescription && (
          <div className="border rounded-md p-4 bg-primary/5">
            <p className="text-sm font-medium mb-2">{t('enhanced-description-0')}</p>
            <p className="text-sm mb-3">{enhancedDescription}</p>
            <Button size="sm" onClick={useEnhancedDescription}>
              {t('use-this-description')}
            </Button>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex gap-2">
        <Button
          variant="outline"
          className="flex-1"
          onClick={enhanceDescription}
          disabled={isEnhancing || isGenerating || !formIntent.trim()}
        >
          {isEnhancing ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Enhancing...
            </>
          ) : (
            <>
              <Sparkles className="mr-2 h-4 w-4" />
              {t('enhance-description-0')}
            </>
          )}
        </Button>
        <Button
          className="flex-1"
          onClick={generateForm}
          disabled={isGenerating || isEnhancing || !formIntent.trim()}
        >
          {isGenerating ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {t('generating-form')}
            </>
          ) : (
            <>
              <Wand2 className="mr-2 h-4 w-4" />
              {t('generate-form')}
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
