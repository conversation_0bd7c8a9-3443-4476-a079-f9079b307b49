"use client"

import type React from "react"

import { useState } from "react"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Palette, Upload, Download, Code, Share2, Copy } from "lucide-react"
import { useTranslations } from "next-intl"

type FormSettingsProps = {
  brandColor: string
  setBrandColor: (color: string) => void
  logo: string
  setLogo: (logo: string) => void
  exportForm: (format: "json" | "csv" | "pdf") => void
  shareUrl: string
}

export function FormSettings({ brandColor, setBrandColor, logo, setLogo, exportForm, shareUrl }: FormSettingsProps) {
  const t  = useTranslations()
  const [activeTab, setActiveTab] = useState("branding")

  const handleLogoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (event) => {
        if (event.target?.result) {
          setLogo(event.target.result as string)
        }
      }
      reader.readAsDataURL(file)
    }
  }

  const copyShareUrl = () => {
    navigator.clipboard
      .writeText(shareUrl)
      .then(() => {
        alert(t('url-copied-to-clipboard'))
      })
      .catch((err) => {
        console.error("Failed to copy URL: ", err)
      })
  }

  return (
    <div className="p-6">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-6">
          <TabsTrigger value="branding" className="flex items-center gap-2">
            <Palette className="h-4 w-4" />
            {t('branding')}
          </TabsTrigger>
          <TabsTrigger value="share" className="flex items-center gap-2">
            <Share2 className="h-4 w-4" />
            {t('share')}
          </TabsTrigger>
          <TabsTrigger value="export" className="flex items-center gap-2">
            <Download className="h-4 w-4" />
            {t('export')}
          </TabsTrigger>
          <TabsTrigger value="integration" className="flex items-center gap-2">
            <Code className="h-4 w-4" />
            {t('integration')}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="branding">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>{t('brand-colors')}</CardTitle>
                <CardDescription>{t('customize-the-colors-to-match-your-brand-identity')}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="brand-color">{t('primary-color')}</Label>
                    <div className="flex items-center gap-3 mt-1.5">
                      <div className="w-10 h-10 rounded-md border" style={{ backgroundColor: brandColor }} />
                      <Input
                        id="brand-color"
                        type="color"
                        value={brandColor}
                        onChange={(e) => setBrandColor(e.target.value)}
                        className="w-24 h-10 p-1"
                      />
                      <Input
                        type="text"
                        value={brandColor}
                        onChange={(e) => setBrandColor(e.target.value)}
                        className="flex-1"
                      />
                    </div>
                  </div>

                  <div className="pt-4">
                    <div className="flex items-center justify-between">
                      <Label>{t('color-preview')}</Label>
                    </div>
                    <div className="mt-2 space-y-2">
                      <Button className="w-full" style={{ backgroundColor: brandColor, borderColor: brandColor }}>
                        {t('primary-button')}
                      </Button>
                      <Button
                        variant="outline"
                        className="w-full"
                        style={{ color: brandColor, borderColor: brandColor }}
                      >
                        {t('secondary-button')}
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>{t('logo')}</CardTitle>
                <CardDescription>{t('upload-your-company-logo-to-display-on-the-form')}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="logo-upload">{t('upload-logo')}</Label>
                    <div className="mt-1.5">
                      <Input id="logo-upload" type="file" accept="image/*" onChange={handleLogoUpload} />
                    </div>
                  </div>

                  <div className="pt-4">
                    <Label>{t('logo-preview')}</Label>
                    <div className="mt-2 border rounded-md p-4 flex items-center justify-center h-32">
                      {logo ? (
                        <img
                          src={logo || "/placeholder.svg"}
                          alt="Company Logo"
                          className="max-h-full max-w-full object-contain"
                        />
                      ) : (
                        <div className="text-gray-400 flex flex-col items-center">
                          <Upload className="h-8 w-8 mb-2" />
                          <span>{t('no-logo-uploaded')}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="share">
          <Card>
            <CardHeader>
              <CardTitle>{t('share-your-form')}</CardTitle>
              <CardDescription>{t('share-your-form-with-others-to-collect-responses')}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label>{t('public-form-url')}</Label>
                  <div className="flex mt-1.5">
                    <Input value={shareUrl} readOnly className="rounded-r-none" />
                    <Button className="rounded-l-none" onClick={copyShareUrl}>
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                  <p className="text-sm text-gray-500 mt-1">{t('anyone-with-this-link-can-view-and-submit-the-form')}</p>
                </div>
                <div className="pt-2">
                  <Label>{t('embed-code')}</Label>
                  <Textarea
                    readOnly
                    value={`<iframe src="${shareUrl}" width="100%" height="600" frameborder="0"></iframe>`}
                    className="mt-1.5 font-mono text-sm"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="export">
          <Card>
            <CardHeader>
              <CardTitle>{t('export-options')}</CardTitle>
              <CardDescription>{t('export-your-form-in-different-formats')}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button
                  variant="outline"
                  className="h-auto py-6 flex flex-col items-center justify-center"
                  onClick={() => exportForm("json")}
                >
                  <div className="rounded-full bg-primary/10 p-2 mb-2">
                    <Download className="h-6 w-6 text-primary" />
                  </div>
                  <span className="font-medium">JSON</span>
                  <span className="text-xs text-gray-500 mt-1">{t('export-form-configuration')}</span>
                </Button>

                <Button
                  variant="outline"
                  className="h-auto py-6 flex flex-col items-center justify-center"
                  onClick={() => exportForm("csv")}
                >
                  <div className="rounded-full bg-primary/10 p-2 mb-2">
                    <Download className="h-6 w-6 text-primary" />
                  </div>
                  <span className="font-medium">CSV</span>
                  <span className="text-xs text-gray-500 mt-1">{t('export-form-responses')}</span>
                </Button>

                <Button
                  variant="outline"
                  className="h-auto py-6 flex flex-col items-center justify-center"
                  onClick={() => exportForm("pdf")}
                >
                  <div className="rounded-full bg-primary/10 p-2 mb-2">
                    <Download className="h-6 w-6 text-primary" />
                  </div>
                  <span className="font-medium">PDF</span>
                  <span className="text-xs text-gray-500 mt-1">{t('export-form-as-document')}</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="integration">
          <Card>
            <CardHeader>
              <CardTitle>{t('api-integration')}</CardTitle>
              <CardDescription>{t('connect-your-form-to-external-services')}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="webhook-url">{t('webhook-url')}</Label>
                  <Input
                    id="webhook-url"
                    type="url"
                    placeholder="https://your-api-endpoint.com/webhook"
                    className="mt-1.5"
                  />
                  <p className="text-sm text-gray-500 mt-1">{t('form-submissions-will-be-sent-to-this-url')}</p>
                </div>

                <div className="pt-2">
                  <Label htmlFor="api-key">{t('api-key-optional')}</Label>
                  <Input id="api-key" type="password" placeholder="Your API key" className="mt-1.5" />
                </div>

                <div className="pt-4">
                  <Button>{t('save-integration-settings')}</Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

