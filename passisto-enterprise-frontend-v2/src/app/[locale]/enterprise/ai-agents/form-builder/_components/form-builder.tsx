"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { DndProvider } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import { FormDesigner } from "./form-designer";
import { FormPreview } from "./form-preview";
import { FormSettings } from "./form-settings";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { PlusCircle, Settings, Eye, Share2, Save } from "lucide-react";
import type { FormField, StoredForm } from "../_lib/types";
import { defaultFormFields } from "../_lib/default-fields";
import { generateSlug } from "../_lib/utils";
import { useTranslations } from "next-intl";

type FormBuilderProps = {
  form: StoredForm;
};

export function FormBuilder({ form }: FormBuilderProps) {
  // Initialize useTranslations with a specific namespace if you have one,
  // otherwise, it will default to the 'messages' object.
  // For clarity, I'm assuming a global 'messages' object, but you might want to scope this.
  const t = useTranslations();
  const router = useRouter();
  const [formTitle, setFormTitle] = useState(form.title);
  const [formDescription, setFormDescription] = useState(form.description);
  const [formFields, setFormFields] = useState<FormField[]>(form.fields);
  const [activeTab, setActiveTab] = useState("design");
  const [brandColor, setBrandColor] = useState(form.brandColor);
  const [logo, setLogo] = useState(form.logo);
  const [shareUrl, setShareUrl] = useState("");
  const [unsavedChanges, setUnsavedChanges] = useState(false);

  // Set up share URL
  useEffect(() => {
    if (typeof window !== "undefined") {
      setShareUrl(`${window.location.origin}/public-page/form/${form.id}`);
    }
  }, [form.id]);

  // Track unsaved changes
  useEffect(() => {
    const hasChanges =
      formTitle !== form.title ||
      formDescription !== form.description ||
      JSON.stringify(formFields) !== JSON.stringify(form.fields) ||
      brandColor !== form.brandColor ||
      logo !== form.logo;

    setUnsavedChanges(hasChanges);
  }, [formTitle, formDescription, formFields, brandColor, logo, form]);

  const addField = (field: FormField) => {
    setFormFields((prev) => [...prev, { ...field, id: `field-${Date.now()}` }]);
  };

  const updateField = (id: string, updates: Partial<FormField>) => {
    setFormFields((prev) => prev.map((field) => (field.id === id ? { ...field, ...updates } : field)));
  };

  const removeField = (id: string) => {
    setFormFields((prev) => prev.filter((field) => field.id !== id));
  };

  const reorderFields = (startIndex: number, endIndex: number) => {
    const result = Array.from(formFields);
    const [removed] = result.splice(startIndex, 1);
    result.splice(endIndex, 0, removed);
    setFormFields(result);
  };

  const saveForm = () => {
    // Get existing forms
    const storedForms = localStorage.getItem("forms");
    const forms = storedForms ? JSON.parse(storedForms) : [];

    // Find and update the current form
    const updatedForms = forms.map((f: StoredForm) => {
      if (f.id === form.id) {
        return {
          ...f,
          title: formTitle,
          description: formDescription,
          fields: formFields,
          brandColor,
          logo,
          // slug: formTitle !== f.title ? generateSlug(formTitle) : f.slug, // Uncomment if you want to regenerate slug on title change
          updatedAt: new Date().toISOString(),
        };
      }
      return f;
    });

    // Save back to localStorage
    localStorage.setItem("forms", JSON.stringify(updatedForms));

    // Update share URL if title changed
    if (formTitle !== form.title && typeof window !== "undefined") {
      const newSlug = generateSlug(formTitle);
      setShareUrl(`${window.location.origin}/public-page/form/${newSlug}`);
    }

    // Reset unsaved changes flag
    setUnsavedChanges(false);

    // Trigger storage event for other tabs
    window.dispatchEvent(new Event("storage"));

    // Show success message
    alert(t("formSavedSuccess")); // Use translation
  };

  const exportForm = (format: "json" | "csv" | "pdf") => {
    const formData = {
      title: formTitle,
      description: formDescription,
      fields: formFields,
      brandColor,
      logo,
    };

    if (format === "json") {
      const dataStr = JSON.stringify(formData, null, 2);
      const dataUri = `data:application/json;charset=utf-8,${encodeURIComponent(dataStr)}`;
      const exportFileDefaultName = `${formTitle.toLowerCase().replace(/\s+/g, "-")}.json`;
      const linkElement = document.createElement("a");
      linkElement.setAttribute("href", dataUri);
      linkElement.setAttribute("download", exportFileDefaultName);
      linkElement.click();
    } else if (format === "pdf") {
      alert(t("exportPdfFutureUpdate")); // Use translation
    } else if (format === "csv") {
      alert(t("exportCsvFutureUpdate")); // Use translation
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <div className="border-b">
          <div className="flex items-center justify-between px-4">
            <TabsList className="h-14">
              <TabsTrigger value="design" className="flex items-center gap-2">
                <PlusCircle className="h-4 w-4" />
                <span className="hidden sm:inline">{t("designTab")}</span>
              </TabsTrigger>
              <TabsTrigger value="preview" className="flex items-center gap-2">
                <Eye className="h-4 w-4" />
                <span className="hidden sm:inline">{t("previewTab")}</span>
              </TabsTrigger>
              <TabsTrigger value="settings" className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                <span className="hidden sm:inline">{t("settingsTab")}</span>
              </TabsTrigger>
            </TabsList>
            <div className="flex items-center gap-2">
              <Button
                variant={unsavedChanges ? "default" : "outline"}
                size="sm"
                onClick={saveForm}
                className="flex items-center gap-1"
              >
                <Save className="h-4 w-4" />
                <span className="hidden sm:inline">{t("saveButton")}</span>
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.open(shareUrl, "_blank")}
                className="flex items-center gap-1"
              >
                <Share2 className="h-4 w-4" />
                <span className="hidden sm:inline">{t("viewButton")}</span>
              </Button>
            </div>
          </div>
        </div>

        <TabsContent value="design" className="p-0">
          <DndProvider backend={HTML5Backend}>
            <div className="grid grid-cols-1 lg:grid-cols-12 min-h-[70vh]">
              <div className="lg:col-span-9 border-r">
                <FormDesigner
                  formTitle={formTitle}
                  setFormTitle={setFormTitle}
                  formDescription={formDescription}
                  setFormDescription={setFormDescription}
                  formFields={formFields}
                  updateField={updateField}
                  removeField={removeField}
                  reorderFields={reorderFields}
                  brandColor={brandColor}
                />
              </div>
              <div className="lg:col-span-3 p-4 border-t lg:border-t-0">
                <div>
                  <h3 className="font-medium text-sm mb-3 text-gray-500 uppercase tracking-wider">
                    {t("addFieldsHeading")}
                  </h3>
                  <div className="space-y-2">
                    {defaultFormFields.map((field) => (
                      <Button
                        key={field.type}
                        variant="outline"
                        className="w-full justify-start text-left"
                        onClick={() => addField(field)}
                      >
                        <field.icon className="mr-2 h-4 w-4" />
                        {t(field.label)} {/* Use translation for field labels */}
                      </Button>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </DndProvider>
        </TabsContent>

        <TabsContent value="preview" className="p-0">
          <FormPreview
            formTitle={formTitle}
            formDescription={formDescription}
            formFields={formFields}
            brandColor={brandColor}
            logo={logo}
          />
        </TabsContent>

        <TabsContent value="settings" className="p-0">
          <FormSettings
            brandColor={brandColor}
            setBrandColor={setBrandColor}
            logo={logo}
            setLogo={setLogo}
            exportForm={exportForm}
            shareUrl={shareUrl}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}