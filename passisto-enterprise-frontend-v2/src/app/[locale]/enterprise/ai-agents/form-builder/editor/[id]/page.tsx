"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { FormBuilder } from "../../_components/form-builder"
import { Button } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import type { StoredForm } from "../../_lib/types"

export default function EditorPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const [form, setForm] = useState<StoredForm | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Load form from localStorage
    const loadForm = () => {
      try {
        const storedForms = localStorage.getItem("forms")
        if (storedForms) {
          const forms = JSON.parse(storedForms) as StoredForm[]
          const foundForm = forms.find((f) => f.id === params.id)
          setForm(foundForm || null)
        }
      } catch (error) {
        console.error("Error loading form:", error)
      } finally {
        setLoading(false)
      }
    }

    loadForm()
  }, [params])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    )
  }

  if (!form) {
    return (
      <div className="min-h-screen p-4 md:p-8 bg-gray-50">
        <div className="max-w-3xl mx-auto text-center">
          <h1 className="text-2xl font-bold mb-4">Form Not Found</h1>
          <p className="mb-6">The form you're looking for doesn't exist or has been deleted.</p>
          <Button onClick={() => router.push("/")}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Dashboard
          </Button>
        </div>
      </div>
    )
  }

  return (
    <main className="p-4 mt-8 md:p-8 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        {/* <div className="mb-6">
          <Button variant="outline" onClick={() => router.push("/")} className="mb-4">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Dashboard
          </Button>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900">Editing: {form.title}</h1>
        </div> */}
        <FormBuilder form={form} />
      </div>
    </main>
  )
}

