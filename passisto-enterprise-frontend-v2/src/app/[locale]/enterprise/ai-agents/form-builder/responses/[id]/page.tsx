"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON>Lef<PERSON>, Download, Trash2 } from "lucide-react"
import type { StoredForm, FormSubmissionData } from "../../_lib/types"

export default function ResponsesPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const [form, setForm] = useState<StoredForm | null>(null)
  const [submissions, setSubmissions] = useState<FormSubmissionData[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Load form and submissions from localStorage
    try {
      // Load form
      const storedForms = localStorage.getItem("forms")
      if (storedForms) {
        const forms = JSON.parse(storedForms) as StoredForm[]
        const foundForm = forms.find((f) => f.id === params.id)
        setForm(foundForm || null)
      }

      // Load submissions
      const storedSubmissions = localStorage.getItem("submissions")
      if (storedSubmissions) {
        const allSubmissions = JSON.parse(storedSubmissions) as FormSubmissionData[]
        const formSubmissions = allSubmissions.filter((s) => s.formId === params.id)
        setSubmissions(formSubmissions)
      }
    } catch (error) {
      console.error("Error loading data:", error)
    } finally {
      setLoading(false)
    }
  }, [params])

  const handleDeleteSubmission = (submissionId: string) => {
    if (confirm("Are you sure you want to delete this submission?")) {
      // Get all submissions
      const storedSubmissions = localStorage.getItem("submissions")
      if (storedSubmissions) {
        const allSubmissions = JSON.parse(storedSubmissions) as FormSubmissionData[]

        // Filter out the deleted submission
        const updatedSubmissions = allSubmissions.filter((s) => s.id !== submissionId)

        // Save back to localStorage
        localStorage.setItem("submissions", JSON.stringify(updatedSubmissions))

        // Update state
        setSubmissions(submissions.filter((s) => s.id !== submissionId))
      }
    }
  }

  // const exportResponses = () => {
  //   if (submissions.length === 0) {
  //     alert("No submissions to export")
  //     return
  //   }

  //   // Convert to CSV
  //   const fields = form?.fields || []
  //   const headers = ["Submission Date", ...fields.map((f) => f.label)]

  //   const csvRows = [
  //     headers.join(","),
  //     ...submissions.map((submission) => {
  //       const date = new Date(submission.submittedAt).toLocaleString()
  //       const values = fields.map((field) => {
  //         const value = submission.data[field.id] || ""
  //         // Escape commas and quotes for CSV
  //         return typeof value === "string" ? `"${value.replace(/"/g, '""')}"` : value
  //       })
  //       return [date, ...values].join(",")
  //     }),
  //   ]

  //   const csvContent = csvRows.join("\n")
  //   const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" })
  //   const url = URL.createObjectURL(blob)
  //   const link = document.createElement("a")
  //   link.setAttribute("href", url)
  //   link.setAttribute("download", `${form?.title || "form"}-responses.csv`)
  //   link.click()
  // }
  // const exportResponses = () => {
  //   if (!form || submissions.length === 0) {
  //     alert("No submissions to export")
  //     return
  //   }

  //   try {
  //     // Convert to CSV
  //     const fields = form.fields
  //     const headers = ["Submission Date", ...fields.map((f) => f.label)]

  //     const csvRows = [headers.join(",")]

  //     // Add each submission as a row
  //     submissions.forEach((submission) => {
  //       const date = new Date(submission.submittedAt).toLocaleString()
  //       const values = fields.map((field) => {
  //         let value = submission.data[field.id]

  //         // Handle different value types
  //         if (value === undefined || value === null) {
  //           value = ""
  //         } else if (typeof value === "object") {
  //           // For file inputs or complex objects
  //           value = "Data Object"
  //         }

  //         // Escape commas and quotes for CSV
  //         if (typeof value === "string") {
  //           // Replace double quotes with two double quotes
  //           value = value.replace(/"/g, '""')
  //           // Wrap in quotes if contains comma, newline or quote
  //           if (value.includes(",") || value.includes("\n") || value.includes('"')) {
  //             value = `"${value}"`
  //           }
  //         }

  //         return value
  //       })

  //       csvRows.push([date, ...values].join(","))
  //     })

  //     const csvContent = csvRows.join("\n")
  //     const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" })
  //     const url = URL.createObjectURL(blob)
  //     const link = document.createElement("a")
  //     link.setAttribute("href", url)
  //     link.setAttribute("download", `${form.title.replace(/\s+/g, "-").toLowerCase()}-responses.csv`)
  //     link.style.visibility = "hidden"
  //     document.body.appendChild(link)
  //     link.click()
  //     document.body.removeChild(link)
  //   } catch (error) {
  //     console.error("Error exporting CSV:", error)
  //     alert("Failed to export responses. Please try again.")
  //   }
  // }
  const exportResponses = () => {
    if (submissions.length === 0) {
      alert("No submissions to export")
      return
    }

    // Convert to CSV
    const fields = form?.fields || []
    const headers = ["Submission Date", ...fields.map((f) => f.label)]

    const csvRows = [
      headers.join(","),
      ...submissions.map((submission) => {
        const date = new Date(submission.submittedAt).toLocaleString("en-US", { hour12: true }) // Forcer le bon format
        const values = fields.map((field) => {
          const value = submission.data[field.id] || ""
          // Escape commas and quotes for CSV
          return typeof value === "string" ? `"${value.replace(/"/g, '""')}"` : value
        })
        return [`"${date}"`, ...values].join(",") // Ajout de guillemets autour de la date
      }),
    ]

    const csvContent = csvRows.join("\n")
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" })
    const url = URL.createObjectURL(blob)
    const link = document.createElement("a")
    link.setAttribute("href", url)
    link.setAttribute("download", `${form?.title || "form"}-responses.csv`)
    link.click()
}


  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    )
  }

  if (!form) {
    return (
      <div className="min-h-screen p-4 md:p-8 bg-gray-50">
        <div className="max-w-3xl mx-auto text-center">
          <h1 className="text-2xl font-bold mb-4">Form Not Found</h1>
          <p className="mb-6">The form you're looking for doesn't exist or has been deleted.</p>
          <Button onClick={() => router.push("/")}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Dashboard
          </Button>
        </div>
      </div>
    )
  }

  return (
    <main className="min-h-screen p-4 md:p-8 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        <div className="mb-6">
          <Button variant="outline" onClick={() => router.push("/")} className="mb-4">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Dashboard
          </Button>
          <div className="flex justify-between items-center">
            <h1 className="text-3xl font-bold tracking-tight text-gray-900">Responses: {form.title}</h1>
            {submissions.length > 0 && (
              <Button onClick={exportResponses}>
                <Download className="mr-2 h-4 w-4" />
                Export CSV
              </Button>
            )}
          </div>
        </div>

        {submissions.length === 0 ? (
          <Card>
            <CardHeader>
              <CardTitle>No Submissions Yet</CardTitle>
              <CardDescription>This form hasn't received any submissions yet.</CardDescription>
            </CardHeader>
            <CardContent>
              <p>Share your form with others to start collecting responses.</p>
              <div className="mt-4">
                <p className="font-medium">Form URL:</p>
                <code className="block p-2 bg-gray-100 rounded mt-1 text-sm">
                  {typeof window !== "undefined" ? `${window.location.origin}/public-page/form/${form.slug}` : ""}
                </code>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-6">
            <p className="text-gray-500">{submissions.length} submissions received</p>

            {submissions.map((submission) => (
              <Card key={submission.id}>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-center">
                    <CardTitle className="text-lg">Submission</CardTitle>
                    <div className="flex items-center gap-2">
                      <Button variant="destructive" size="sm" onClick={() => handleDeleteSubmission(submission.id)}>
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  <CardDescription>Submitted on {new Date(submission.submittedAt).toLocaleString()}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {form.fields.map((field) => (
                      <div key={field.id} className="space-y-1">
                        <p className="text-sm font-medium text-gray-500">{field.label}</p>
                        <p className="break-words">
                          {submission.data[field.id] !== undefined && submission.data[field.id] !== null ? (
                            String(submission.data[field.id])
                          ) : (
                            <span className="text-gray-400 italic">No response</span>
                          )}
                        </p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </main>
  )
}


