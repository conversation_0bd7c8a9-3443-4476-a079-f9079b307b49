"use client"

import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Edit, Eye, Trash2, <PERSON><PERSON><PERSON>, ArrowLeft, Plus } from "lucide-react"
import type { StoredForm } from "../_lib/types"
import { useTranslations } from "next-intl"

export default function DashboardPage() {
  const router = useRouter()
  const [forms, setForms] = useState<StoredForm[]>([])
  const [loading, setLoading] = useState(true)
  const t = useTranslations()

  useEffect(() => {
    // Load forms from localStorage
    const loadForms = () => {
      try {
        const storedForms = localStorage.getItem("forms")
        if (storedForms) {
          setForms(JSON.parse(storedForms))
        }
      } catch (error) {
        console.error("Error loading forms:", error)
      } finally {
        setLoading(false)
      }
    }

    loadForms()

    // Add event listener for storage changes
    window.addEventListener("storage", loadForms)

    return () => {
      window.removeEventListener("storage", loadForms)
    }
  }, [])

  const handleEditForm = (formId: string) => {
    router.push(`/enterprise/ai-agents/form-builder/editor/${formId}`)
  }

  const handleViewForm = (formId: string) => {
    router.push(`/public-page/form/${forms.find((f) => f.id === formId)?.slug || ""}`)
  }

  const handleViewResponses = (formId: string) => {
    router.push(`/enterprise/ai-agents/form-builder/responses/${formId}`)
  }

  const handleDeleteForm = (formId: string) => {
    if (confirm(t('are-you-sure-you-want-to-delete-this-form'))) {
      const updatedForms = forms.filter((form) => form.id !== formId)
      localStorage.setItem("forms", JSON.stringify(updatedForms))
      setForms(updatedForms)

      // Trigger storage event for other tabs
      window.dispatchEvent(new Event("storage"))
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    )
  }

  return (
    <main className="min-h-screen p-4 md:p-8 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <Button variant="outline" asChild className="mb-2">
                <Link href="/" className="flex items-center gap-2">
                  <ArrowLeft className="h-4 w-4" />
                  {t('back-to-home')}
                </Link>
              </Button>
              <h1 className="text-3xl font-bold tracking-tight text-gray-900">{t('your-forms-dashboard')}</h1>
            </div>
            <Button asChild>
              <Link href="/" className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                {t('create-new-form')}
              </Link>
            </Button>
          </div>
        </div>

        {forms.length === 0 ? (
          <Card>
            <CardHeader>
              <CardTitle>{t('no-forms-yet')}</CardTitle>
              <CardDescription>{t('you-havent-created-any-forms-yet-use-the-form-builder-to-get-started')}</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="mb-4">
                {t('create-your-first-form-by-describing-what-you-need-and-our-ai-will-generate-it-for-you')}
              </p>
              <Button asChild>
                <Link href="/">
                  <Plus className="mr-2 h-4 w-4" />
                  {t('create-your-first-form')}
                </Link>
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {forms.map((form) => (
              <Card key={form.id} className="overflow-hidden">
                <CardHeader className="pb-2">
                  <CardTitle className="truncate">{form.title}</CardTitle>
                  <CardDescription>Created: {new Date(form.createdAt).toLocaleDateString()}</CardDescription>
                </CardHeader>
                <CardContent className="pb-2">
                  <p className="text-sm text-gray-500 line-clamp-2">{form.description || t('no-description')}</p>
                  <p className="text-sm mt-1">{form.fields.length} fields</p>
                </CardContent>
                <CardFooter className="flex justify-between pt-2 border-t">
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline" onClick={() => handleEditForm(form.id)}>
                      <Edit className="h-4 w-4 mr-1" /> {t('edit')}
                    </Button>
                    <Button size="sm" variant="outline" onClick={() => handleViewForm(form.id)}>
                      <Eye className="h-4 w-4 mr-1" /> {t('view')}
                    </Button>
                  </div>
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline" onClick={() => handleViewResponses(form.id)}>
                      <BarChart className="h-4 w-4 mr-1" /> {t('responses')}
                    </Button>
                    <Button size="sm" variant="destructive" onClick={() => handleDeleteForm(form.id)}>
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </CardFooter>
              </Card>
            ))}
          </div>
        )}
      </div>
    </main>
  )
}

