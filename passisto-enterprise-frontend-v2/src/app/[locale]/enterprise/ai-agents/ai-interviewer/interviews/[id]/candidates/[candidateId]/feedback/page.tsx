"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  ArrowLeft,
  Download,
  Star,
  ThumbsUp,
  ThumbsDown,
  AlertTriangle,
  User,
} from "lucide-react";
import Link from "next/link";
import axiosInstance from "@/config/axios";
import { GET_FEEDBACK_BY_INTERVIEW_ID_AND_CANDIDATE_ID } from "@/utils/routes";
import { useBackendUser } from "@/hooks/useBackendUser";
import { checkPermissions, interviewPermissions } from "@/utils/ACTION_PERMISSIONS";
import { toast } from "sonner";

interface CategoryScore {
  name: string;
  score: number;
  comment: string;
}

interface Feedback {
  id: string;
  userId: string;
  candidateName: string;
  candidateEmail: string;
  interviewId: string;
  interviewRole: string;
  interviewLevel: string;
  totalScore: number;
  categoryScores: CategoryScore[];
  strengths: string[];
  areasForImprovement: string[];
  finalAssessment: string;
  createdAt: string;
}

export default function CandidateFeedbackPage() {
  const params = useParams<{ id: string; candidateId: string }>();
  const router = useRouter();
  const { backendUser, loading: backendUserLoading } = useBackendUser();

  const [feedback, setFeedback] = useState<Feedback | null>(null);
  const [loading, setLoading] = useState(true);
  const fetchFeedback = async (inteviewId: string, candidateId: String) => {
    try {
      const response = await axiosInstance.get<Feedback>(
        GET_FEEDBACK_BY_INTERVIEW_ID_AND_CANDIDATE_ID(inteviewId, candidateId)
      );
      console.log(response.data);
      setFeedback(response.data);
      setLoading(false);
    } catch (error) {}
  };


  useEffect(() => {
      // Only check permissions after backendUser has loaded
      if (!backendUserLoading && backendUser) {
        const permissions = backendUser?.permissions ?? [];
  
        // Check if user has at least one interview-related permission
        const hasAnyInterviewPermission = checkPermissions(permissions, [
          interviewPermissions.canView,
          interviewPermissions.canViewFeedback,
        ]);
  
        // Redirect if user doesn't have any interview-related permissions
        if (!hasAnyInterviewPermission) {
          toast.error("Permission denied", {
            description:
              "You don't have permission to access the interviews page.",
          });
          router.back();
        }
      }
    }, [backendUser, backendUserLoading, router]);

  useEffect(() => {
    console.log(params.id);
    console.log(params.candidateId);
    if (params.id && params.candidateId) {
      fetchFeedback(params.id, params.candidateId);
    }
  }, [params?.id, params.candidateId]);

  if (loading) {
    return (
      <div className="container mx-auto py-8 px-4 flex items-center justify-center min-h-[60vh]">
        <div className="flex flex-col items-center">
          <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
          <p className="mt-4 text-muted-foreground">
            Loading candidate feedback...
          </p>
        </div>
      </div>
    );
  }

  if (!feedback) {
    return (
      <div className="container mx-auto py-8 px-4">
        <Card>
          <CardContent className="pt-6 flex flex-col items-center">
            <AlertTriangle className="h-16 w-16 text-destructive mb-4" />
            <h2 className="text-xl font-bold mb-2">Feedback Not Found</h2>
            <p className="text-center text-muted-foreground mb-6">
              We couldn't find the feedback for this candidate. It may have been
              deleted or doesn't exist.
            </p>
            <Link href="/enterprise/ai-agents/ai-interviewer/interviews">
              <Button>Return to Interviews</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Helper function to get color based on score
  const getScoreColor = (score: number) => {
    if (score >= 80) return "bg-green-500";
    if (score >= 60) return "bg-yellow-500";
    return "bg-red-500";
  };

  // Helper function to get text color based on score
  const getScoreTextColor = (score: number) => {
    if (score >= 80) return "text-green-700";
    if (score >= 60) return "text-yellow-700";
    return "text-red-700";
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <div>
          <div className="flex items-center gap-2 mb-2">
            <Link href="/enterprise/ai-agents/ai-interviewer/interviews">
              <Button variant="ghost" size="icon">
                <ArrowLeft className="h-4 w-4" />
              </Button>
            </Link>
            <h1 className="text-2xl font-bold">Candidate Feedback</h1>
          </div>
          <p className="text-muted-foreground">
            Generated on {formatDate(feedback.createdAt)}
          </p>
        </div>
        <Button variant="outline" className="gap-2">
          <Download className="h-4 w-4" />
          Export Report
        </Button>
      </div>

      {/* Candidate Info Card */}
      <Card className="mb-8">
        <CardHeader className="pb-2">
          <CardTitle>Candidate Information</CardTitle>
          <CardDescription>
            Details about the candidate and interview
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 md:grid-cols-2">
            <div>
              <div className="flex items-center gap-3 mb-4">
                <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
                  <User className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <h3 className="font-medium">{feedback.candidateName}</h3>
                  <p className="text-sm text-muted-foreground">
                    {feedback.candidateEmail}
                  </p>
                </div>
              </div>
            </div>
            <div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">Position</p>
                  <p className="font-medium">{feedback.interviewRole}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Level</p>
                  <p className="font-medium capitalize">
                    {feedback.interviewLevel}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">
                    Interview Date
                  </p>
                  <p className="font-medium">
                    {formatDate(feedback.createdAt)}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Overall Score</p>
                  <p className="font-medium">{feedback.totalScore}/100</p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Overall Score Card */}
      <Card className="mb-8">
        <CardHeader className="pb-2">
          <CardTitle>Overall Assessment</CardTitle>
          <CardDescription>Summary of interview performance</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-6 items-center md:items-start">
            <div className="flex flex-col items-center">
              <div className="relative w-32 h-32 flex items-center justify-center">
                <svg className="w-full h-full" viewBox="0 0 100 100">
                  <circle
                    className="text-muted stroke-current"
                    strokeWidth="10"
                    cx="50"
                    cy="50"
                    r="40"
                    fill="transparent"
                  ></circle>
                  <circle
                    className={`${getScoreTextColor(
                      feedback.totalScore
                    )} stroke-current`}
                    strokeWidth="10"
                    strokeLinecap="round"
                    cx="50"
                    cy="50"
                    r="40"
                    fill="transparent"
                    strokeDasharray={`${2 * Math.PI * 40}`}
                    strokeDashoffset={`${
                      2 * Math.PI * 40 * (1 - feedback.totalScore / 100)
                    }`}
                    transform="rotate(-90 50 50)"
                  ></circle>
                </svg>
                <div className="absolute flex flex-col items-center justify-center">
                  <span className="text-3xl font-bold">
                    {feedback.totalScore}
                  </span>
                  <span className="text-xs text-muted-foreground">
                    out of 100
                  </span>
                </div>
              </div>
              <div className="mt-2 flex items-center gap-1">
                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                <Star
                  className={`h-4 w-4 ${
                    feedback.totalScore >= 80
                      ? "fill-yellow-400 text-yellow-400"
                      : "text-muted-foreground"
                  }`}
                />
              </div>
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold mb-2">Final Assessment</h3>
              <p className="text-muted-foreground">
                {feedback.finalAssessment}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Category Scores */}
      <div className="grid gap-6 md:grid-cols-2 mb-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ThumbsUp className="h-5 w-5 text-green-500" />
              Strengths
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {feedback.strengths.map((strength, index) => (
                <li key={index} className="flex items-start gap-2">
                  <div className="mt-1 min-w-4 h-4 rounded-full bg-green-500"></div>
                  <span>{strength}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ThumbsDown className="h-5 w-5 text-yellow-500" />
              Areas for Improvement
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {feedback.areasForImprovement.map((area, index) => (
                <li key={index} className="flex items-start gap-2">
                  <div className="mt-1 min-w-4 h-4 rounded-full bg-yellow-500"></div>
                  <span>{area}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Category Scores */}
      <Card>
        <CardHeader>
          <CardTitle>Detailed Category Scores</CardTitle>
          <CardDescription>
            Breakdown of performance by category
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {feedback.categoryScores.map((category, index) => (
              <div key={index}>
                <div className="flex justify-between items-center mb-2">
                  <div className="flex items-center gap-2">
                    <h3 className="font-medium">{category.name}</h3>
                    <Badge variant="outline">{category.score}/100</Badge>
                  </div>
                  <span
                    className={`font-semibold ${getScoreTextColor(
                      category.score
                    )}`}
                  >
                    {category.score >= 80
                      ? "Excellent"
                      : category.score >= 60
                      ? "Good"
                      : "Needs Improvement"}
                  </span>
                </div>
                <div className="w-full bg-muted rounded-full h-2.5 mb-2">
                  <div
                    className={`h-2.5 rounded-full ${getScoreColor(
                      category.score
                    )}`}
                    style={{ width: `${category.score}%` }}
                  ></div>
                </div>
                <p className="text-sm text-muted-foreground">
                  {category.comment}
                </p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
