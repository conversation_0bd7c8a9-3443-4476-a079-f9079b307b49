"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, Plus, FileText, Code, Users, Target, AlertCircle, CheckCircle2, Hash } from "lucide-react"
import { toast } from "sonner"
import { useRouter } from "next/navigation"
import Link from "next/link"
import axiosInstance from "@/config/axios"
import { CREATE_INTERVIEW } from "@/utils/routes"
import { checkPermissions, interviewPermissions } from "@/utils/ACTION_PERMISSIONS"
import { useBackendUser } from "@/hooks/useBackendUser"
import { useTranslations } from "next-intl" // Import useTranslations

interface InterviewTemplate {
  _id: string
  role: string
  level: string
  techstack: string[]
  createdAt: string
  createdBy: string
  type: string
  questions: string[]
  questionCount?: number
}

export default function EditInterviewTemplate() {
  const t = useTranslations("createInterviewTemplatePage") // Initialize useTranslations hook
  const { backendUser, loading: backendUserLoading } = useBackendUser()

  const [template, setTemplate] = useState<InterviewTemplate | null>(null)
  const [loading, setLoading] = useState(true)
  const [role, setRole] = useState("")
  const [level, setLevel] = useState("")
  const [techStack, setTechStack] = useState<string[]>([])
  const [techInput, setTechInput] = useState("")
  const [type, setType] = useState("")
  const [questionCount, setQuestionCount] = useState<number>(10)
  const [saving, setSaving] = useState(false)

  const router = useRouter()

  useEffect(() => {
    if (!backendUserLoading && backendUser) {
      const permissions = backendUser?.permissions ?? []
      const hasAnyInterviewPermission = checkPermissions(permissions, [interviewPermissions.canCreate])

      if (!hasAnyInterviewPermission) {
        toast.error(t("permissionDeniedToastTitle"), {
          description: t("permissionDeniedToastDescription"),
        })
        router.back()
      }
    }
  }, [backendUser, backendUserLoading, router, t]) // Add t to dependency array

  const handleAddTech = () => {
    if (techInput.trim() && !techStack.includes(techInput.trim())) {
      setTechStack([...techStack, techInput.trim()])
      setTechInput("")
    }
  }

  const handleRemoveTech = (tech: string) => {
    setTechStack(techStack.filter((t) => t !== tech))
  }

  const handleQuestionCountChange = (value: string) => {
    const count = Number.parseInt(value)
    if (!isNaN(count) && count >= 1 && count <= 50) {
      setQuestionCount(count)
    }
  }

  const isFormValid = () => {
    return role.trim() && level && type && techStack.length > 0 && questionCount >= 1 && questionCount <= 50
  }

  const handleSaveTemplate = async () => {
    if (!isFormValid()) {
      toast.error(t("missingInformationToastTitle"), {
        description: t("missingInformationToastDescription"),
      })
      return
    }

    setSaving(true)
    try {
      const response = await axiosInstance.post(CREATE_INTERVIEW, {
        role: role.trim(),
        level,
        type,
        techstack: techStack,
        amount:questionCount,
        createdBy: backendUser?.fullName,
        companyId: backendUser?.companyId,
        companyName: backendUser?.companyName,
      })

      if (response.status === 201) {
        toast.success(t("templateCreatedToastTitle"), {
          description: t("templateCreatedToastDescription", { questionCount }),
        })
        router.push("/enterprise/ai-agents/ai-interviewer/interviews")
      } else {
        throw new Error("Failed to create template")
      }
    } catch (error) {
      toast.error(t("creationFailedToastTitle"), {
        description: t("creationFailedToastDescription"),
      })
    } finally {
      setSaving(false)
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "technical":
        return <Code className="h-4 w-4" />
      case "behavioral":
        return <Users className="h-4 w-4" />
      case "system-design":
        return <Target className="h-4 w-4" />
      default:
        return <FileText className="h-4 w-4" />
    }
  }

  const getRecommendedQuestionCount = () => {
    switch (type) {
      case "technical":
        return "8-12 questions" // These can also be translated
      case "behavioral":
        return "6-10 questions"
      case "system-design":
        return "3-5 questions"
      case "mixed":
        return "10-15 questions"
      default:
        return "5-15 questions"
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100/50">
      <div className="container mx-auto py-8 px-4 max-w-4xl">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Link href="/enterprise/ai-agents/ai-interviewer/interviews">
            <Button variant="ghost" size="icon" className="rounded-full">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-slate-900">{t("pageTitle")}</h1>
            <p className="text-slate-600 mt-1">
              {t("pageDescription")}
            </p>
          </div>
        </div>

        <div className="grid gap-8">
          {/* Basic Information */}
          <Card className="shadow-sm border-0 bg-white/70 backdrop-blur-sm">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2 text-lg">
                <FileText className="h-5 w-5 text-blue-600" />
                {t("basicInformationTitle")}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="role" className="text-sm font-medium text-slate-700">
                    {t("roleTitleLabel")} <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="role"
                    placeholder={t("roleTitlePlaceholder")}
                    value={role}
                    onChange={(e) => setRole(e.target.value)}
                    className="h-11"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="level" className="text-sm font-medium text-slate-700">
                    {t("experienceLevelLabel")} <span className="text-red-500">*</span>
                  </Label>
                  <Select value={level} onValueChange={setLevel}>
                    <SelectTrigger id="level" className="h-11">
                      <SelectValue placeholder={t("selectExperienceLevelPlaceholder")} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="junior">{t("juniorLevel")}</SelectItem>
                      <SelectItem value="mid">{t("midLevel")}</SelectItem>
                      <SelectItem value="senior">{t("seniorLevel")}</SelectItem>
                      <SelectItem value="lead">{t("leadLevel")}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="type" className="text-sm font-medium text-slate-700">
                  {t("interviewTypeLabel")} <span className="text-red-500">*</span>
                </Label>
                <Select value={type} onValueChange={setType}>
                  <SelectTrigger id="type" className="h-11">
                    <SelectValue placeholder={t("selectInterviewTypePlaceholder")} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="technical">
                      <div className="flex items-center gap-2">
                        <Code className="h-4 w-4" />
                        {t("technicalInterview")}
                      </div>
                    </SelectItem>
                    <SelectItem value="behavioral">
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4" />
                        {t("behavioralInterview")}
                      </div>
                    </SelectItem>
                    <SelectItem value="system-design">
                      <div className="flex items-center gap-2">
                        <Target className="h-4 w-4" />
                        {t("systemDesign")}
                      </div>
                    </SelectItem>
                    <SelectItem value="mixed">
                      <div className="flex items-center gap-2">
                        <FileText className="h-4 w-4" />
                        {t("mixedInterview")}
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Tech Stack */}
          <Card className="shadow-sm border-0 bg-white/70 backdrop-blur-sm">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2 text-lg">
                <Code className="h-5 w-5 text-green-600" />
                {t("technologyStackTitle")} <span className="text-red-500 text-sm font-normal">*</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-3">
                <Input
                  id="tech-stack"
                  placeholder={t("techStackPlaceholder")}
                  value={techInput}
                  onChange={(e) => setTechInput(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      e.preventDefault()
                      handleAddTech()
                    }
                  }}
                  className="h-11"
                />
                <Button type="button" onClick={handleAddTech} disabled={!techInput.trim()} className="h-11 px-6">
                  <Plus className="h-4 w-4 mr-1" />
                  {t("addButton")}
                </Button>
              </div>

              {techStack.length > 0 && (
                <div className="flex flex-wrap gap-2 p-4 bg-slate-50 rounded-lg border">
                  {techStack.map((tech) => (
                    <Badge
                      key={tech}
                      variant="secondary"
                      className="flex items-center gap-2 px-3 py-1 bg-blue-100 text-blue-800 hover:bg-blue-200 transition-colors"
                    >
                      {tech}
                      <button
                        onClick={() => handleRemoveTech(tech)}
                        className="ml-1 rounded-full hover:bg-blue-300 p-0.5 transition-colors"
                      >
                        ×
                      </button>
                    </Badge>
                  ))}
                </div>
              )}

              {techStack.length === 0 && (
                <div className="text-center py-8 text-slate-500">
                  <Code className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>{t("noTechnologiesAdded")}</p>
                  <p className="text-sm">{t("addTechnologiesHint")}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Question Configuration */}
          <Card className="shadow-sm border-0 bg-white/70 backdrop-blur-sm">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2 text-lg">
                <Hash className="h-5 w-5 text-purple-600" />
                {t("aiQuestionGenerationTitle")}
              </CardTitle>
              <p className="text-sm text-slate-600 mt-1">
                {t("aiQuestionGenerationDescription")}
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="question-count" className="text-sm font-medium text-slate-700">
                    {t("numberOfQuestionsLabel")} <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="question-count"
                    type="number"
                    min="1"
                    max="50"
                    placeholder={t("numberOfQuestionsPlaceholder")}
                    value={questionCount}
                    onChange={(e) => handleQuestionCountChange(e.target.value)}
                    className="h-11"
                  />
                  <p className="text-xs text-slate-500">{t("questionCountHint")}</p>
                </div>
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-slate-700">{t("recommendedRangeLabel")}</Label>
                  <div className="h-11 px-3 py-2 bg-slate-50 border border-slate-200 rounded-md flex items-center">
                    <span className="text-sm text-slate-600">
                      {type ? getRecommendedQuestionCount() : t("selectInterviewTypeFirst")}
                    </span>
                  </div>
                  <p className="text-xs text-slate-500">{t("basedOnInterviewType")}</p>
                </div>
              </div>

              {/* Question Count Preview */}
              <div className="p-4 bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg border border-purple-100">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                    <Hash className="h-5 w-5 text-purple-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-slate-800">
                      {t("questionsGeneratedTitle", { questionCount })}
                    </h4>
                    <p className="text-sm text-slate-600">
                      {t("questionsGeneratedDescription", { questionCount, type })}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Form Validation Summary */}
          {!isFormValid() && (
            <Card className="border-amber-200 bg-amber-50">
              <CardContent className="pt-6">
                <div className="flex items-start gap-3">
                  <AlertCircle className="h-5 w-5 text-amber-600 flex-shrink-0 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-amber-800 mb-2">{t("completeRequiredFieldsTitle")}</h4>
                    <ul className="text-sm text-amber-700 space-y-1">
                      {!role.trim() && <li>• {t("roleRequired")}</li>}
                      {!level && <li>• {t("levelRequired")}</li>}
                      {!type && <li>• {t("typeRequired")}</li>}
                      {techStack.length === 0 && <li>• {t("techStackRequired")}</li>}
                      {(questionCount < 1 || questionCount > 50) && <li>• {t("questionCountRange")}</li>}
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Actions */}
          <div className="flex justify-end gap-3 pt-4">
            <Link href="/enterprise/ai-agents/ai-interviewer/interviews">
              <Button variant="outline" className="h-11 px-6">
                {t("cancelButton")}
              </Button>
            </Link>
            <Button onClick={handleSaveTemplate} disabled={!isFormValid() || saving} className="h-11 px-8">
              {saving ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                  {t("creatingButton")}
                </>
              ) : (
                <>
                  <CheckCircle2 className="h-4 w-4 mr-2" />
                  {t("createTemplateButton")}
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}