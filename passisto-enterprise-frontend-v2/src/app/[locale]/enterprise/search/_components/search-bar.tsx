"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Camera, Mic, Search, X } from "lucide-react";
import SearchSuggestions from "./search-suggestions";
import { cn } from "@/lib/utils";
import React from "react";
import { useTranslations } from "next-intl";

interface SearchBarProps {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  showSuggestions: boolean;
  setShowSuggestions: (show: boolean) => void;
  handleSearch: () => void;
  setHasSearched: (hasSearched: boolean) => void;
  isLoading?: boolean;
}

export default function SearchBar({
  searchQuery,
  setSearchQuery,
  showSuggestions,
  setShowSuggestions,
  handleSearch,
  setHasSearched,
  isLoading = false,
}: Readonly<SearchBarProps>) {
  const t = useTranslations("searchPage");

  return (
    <div className="relative w-full">
      <div className="relative">
        <Input
          className={cn(
            "w-full h-14 pl-12 pr-40 text-lg rounded-2xl border-2 border-muted-foreground/20 hover:border-primary/50 focus-visible:ring-2 focus-visible:ring-primary/50 transition-all duration-300",
            isLoading && "opacity-70"
          )}
          placeholder={t('search-anything')}
          value={searchQuery}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
            setSearchQuery(e.target.value);
            setShowSuggestions(true);
          }}
          onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {
            if (e.key === "Enter") handleSearch();
          }}
          disabled={isLoading}
        />
        <div
          className={cn(
            "absolute left-4 top-4 h-6 w-6 text-muted-foreground",
            isLoading && "animate-pulse"
          )}
        >
          <Search className="h-6 w-6" />
        </div>

        {searchQuery && !isLoading && (
          <button
            onClick={() => {
              setSearchQuery("");
              setShowSuggestions(false);
              setHasSearched(false);
            }}
            className="absolute right-32 top-4 p-1 rounded-full hover:bg-muted"
          >
            <X className="h-5 w-5 text-muted-foreground" />
          </button>
        )}

        <div className="absolute right-4 top-2 flex space-x-2">
          <Button
            variant="ghost"
            size="icon"
            className="rounded-xl hover:bg-primary hover:text-primary-foreground"
            disabled={isLoading}
          >
            <Mic className="h-5 w-5" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="rounded-xl hover:bg-primary hover:text-primary-foreground"
            disabled={isLoading}
          >
            <Camera className="h-5 w-5" />
          </Button>
        </div>
      </div>

      {showSuggestions && searchQuery && !isLoading && (
        <SearchSuggestions
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          setShowSuggestions={setShowSuggestions}
          handleSearch={handleSearch}
        />
      )}
    </div>
  );
}
