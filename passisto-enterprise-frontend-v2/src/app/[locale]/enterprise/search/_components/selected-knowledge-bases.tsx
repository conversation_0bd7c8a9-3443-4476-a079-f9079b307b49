"use client"

import React from "react"
import { Database } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { useChatSettings } from "@/context/ChatSettingsContext"
import { useTranslations } from "next-intl"

interface SelectedKnowledgeBasesProps {
  className?: string;
}

export default function SelectedKnowledgeBases({ className }: Readonly<SelectedKnowledgeBasesProps>) {
  const t = useTranslations("SearchPage")
  const { selectedAliases } = useChatSettings()

  if (selectedAliases.length === 0) {
    return null
  }

  return (
    <div className={`text-xs text-muted-foreground flex flex-wrap items-center gap-1 ${className}`}>
      <div className="flex items-center">
        <Database className="h-3 w-3 mr-1" />
        <span className="font-semibold mr-1">{t('searching-in')}</span>
      </div>
      {selectedAliases.map((alias) => (
        <Badge 
          key={alias.id} 
          variant="outline" 
          className="text-[10px] py-0 px-1.5 bg-secondary/30"
        >
          {alias.name}
        </Badge>
      ))}
    </div>
  )
}
