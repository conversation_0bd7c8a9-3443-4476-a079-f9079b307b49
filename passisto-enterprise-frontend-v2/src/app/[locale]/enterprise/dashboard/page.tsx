"use client"

import { useEffe<PERSON>, useState } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  <PERSON>,
  <PERSON><PERSON>hart,
  TrendingUp,
  UsersRound,
  UserPlus,
  Mail,
  Sparkles
} from "lucide-react"
import {
  <PERSON><PERSON><PERSON> as RechartsBar<PERSON>hart,
  Bar,
  PieChart,
  Pie,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Cell
} from 'recharts'
import type { User } from "@/lib/mock-data"
import type { Team } from "@/lib/mock-data"
import { Button } from "@/components/ui/button"
import { useRouter } from "next/navigation"
import { useTranslations } from "next-intl" // Ensure this is imported

interface DashboardStats {
  totalTeams: number
  totalUsers: number
  activeUsers: number
  teamsWithMembers: number
  teamDistribution: Array<{ name: string; members: number }>
  userGrowth: Array<{ month: string; users: number }>
  teamActivities: Array<{ name: string; value: number }>
  interviewStats: {
    total: number
    completed: number
    pending: number
  }
  emailStats: {
    total: number
    sent: number
    opened: number
  }
  formStats: {
    total: number
    submissions: number
    conversionRate: number
  }
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8']


export default function Page() {
  const t = useTranslations("DashboardPage") // Specify the namespace
  const router = useRouter()
  const [stats, setStats] = useState<DashboardStats>({
    totalTeams: 0,
    totalUsers: 0,
    activeUsers: 0,
    teamsWithMembers: 0,
    teamDistribution: [],
    userGrowth: [],
    teamActivities: [],
    interviewStats: {
      total: 0,
      completed: 0,
      pending: 0
    },
    emailStats: {
      total: 0,
      sent: 0,
      opened: 0
    },
    formStats: {
      total: 0,
      submissions: 0,
      conversionRate: 0
    }
  })

  useEffect(() => {
    const loadStats = async () => {
      try {
        // Charger les données existantes
        const teams: Team[] = JSON.parse(localStorage.getItem("teams") || "[]")
        const users: User[]  = JSON.parse(localStorage.getItem("users") || "[]")

        // Charger les nouvelles données
        const interviews = JSON.parse(localStorage.getItem("interviews") || "[]")
        const emails = JSON.parse(localStorage.getItem("emails") || "[]")
        const forms = JSON.parse(localStorage.getItem("forms") || "[]")

        const teamDistribution = teams.map(team=> ({
          name: team.name, // Team names might not be translated unless explicitly managed
          members: users.filter(user => user.teams.includes(team.name)).length
        }))

        const teamsWithMembers = teams.filter(team =>
          users.some(user => user.teams.includes(team.name))
        ).length

        // Monthly user growth, using translated month names
        const userGrowth = [
          { month: t('jan'), users: 20 },
          { month: t('feb'), users: 25 },
          { month: t('mar'), users: 30 },
          { month: t('apr'), users: 35 },
          { month: t('may'), users: 45 },
          { month: t('jun'), users: users.length }
        ]

        // Team activity status, using translated labels
        const teamActivities = [
          { name: t('active'), value: teamsWithMembers },
          { name: t('inactive'), value: teams.length - teamsWithMembers }
        ]

        setStats({
          totalTeams: teams.length,
          totalUsers: users.length,
          activeUsers: users.filter(user => user.teams.length > 0).length,
          teamsWithMembers,
          teamDistribution,
          userGrowth,
          teamActivities,
          interviewStats: {
            total: interviews.length,
            completed: interviews.filter((i: any) => i.status === 'completed').length,
            pending: interviews.filter((i: any) => i.status === 'pending').length
          },
          emailStats: {
            total: emails.length,
            sent: emails.filter((e: any) => e.status === 'sent').length,
            opened: emails.filter((e: any) => e.opened).length
          },
          formStats: {
            total: forms.length,
            submissions: forms.reduce((acc: number, form: any) => acc + (form.submissions || 0), 0),
            conversionRate: forms.length > 0 ?
              (forms.reduce((acc: number, form: any) => acc + (form.submissions || 0), 0) / forms.length) * 100 : 0
          }
        })
      } catch (error) {
        console.error("Error loading stats:", error)
      }
    }

    loadStats()
  }, [t]) // Add t to the dependency array to re-run effect when locale changes

  return (
    <div className="flex flex-1 flex-col gap-6 p-4">
      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>{t('quick-actions')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button
              variant="outline"
              className="flex items-center gap-2"
              onClick={() => router.push("/enterprise/ai-agents/ai-interviewer/create")}
            >
              <UserPlus className="h-4 w-4" />
              {t('ai-interview')}
            </Button>

            <Button
              variant="outline"
              className="flex items-center gap-2"
              onClick={() => router.push("/enterprise/ai-agents/email-builder")}
            >
              <Mail className="h-4 w-4" />
              {t('email-builder')}
            </Button>

            <Button
              variant="outline"
              className="flex items-center gap-2"
              onClick={() => router.push("/enterprise/ai-agents/form-builder")}
            >
              <Sparkles className="h-4 w-4" />
              {t('form-builder')}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Main Statistics */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('total-teams')}</CardTitle>
            <UsersRound className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalTeams}</div>
            <p className="text-xs text-muted-foreground">
              {stats.teamsWithMembers} {t('active-teams')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('total-users')}</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalUsers}</div>
            <p className="text-xs text-muted-foreground">
              {stats.activeUsers} {t('active-users')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('team-activity-rate')}</CardTitle>
            <BarChart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats.totalTeams ? Math.round((stats.teamsWithMembers / stats.totalTeams) * 100) : 0}%
            </div>
            <p className="text-xs text-muted-foreground">
              {t('of-teams-have-members')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('avg-users-team')}</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats.totalTeams ? Math.round(stats.totalUsers / stats.totalTeams) : 0}
            </div>
            <p className="text-xs text-muted-foreground">
              {t('users-per-team')}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* New AI Tools Statistics */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('interviews')}</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.interviewStats.total}</div>
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>{t('interview-completed', { count: stats.interviewStats.completed })}</span>
              <span>{t('interview-pending', { count: stats.interviewStats.pending })}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('email-builder')}</CardTitle>
            <BarChart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.emailStats.total}</div>
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>{t('email-sent', { count: stats.emailStats.sent })}</span>
              <span>{t('email-opened', { count: stats.emailStats.opened })}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('forms')}</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.formStats.total}</div>
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>{t('form-submissions', { count: stats.formStats.submissions })}</span>
              <span>{stats.formStats.conversionRate.toFixed(1)} {t('conv-rate')}</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="col-span-4">
          <CardHeader>
            <CardTitle>{t('user-growth')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-[300px] w-full">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={stats.userGrowth}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="users"
                    stroke="#8884d8"
                    strokeWidth={2}
                    name={t('users')}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <Card className="col-span-3">
          <CardHeader>
            <CardTitle>{t('team-status')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-[300px] w-full">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={stats.teamActivities}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {stats.teamActivities.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}