"use client"

import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useChatSessionContext } from '@/context/ChatSessionContext'
import { Loader2 } from 'lucide-react'

function ChatPage() {
  const router = useRouter()
  const { createNewChat } = useChatSessionContext()
  const [isRedirecting, setIsRedirecting] = useState(false)

  useEffect(() => {
    // Prevent multiple redirects
    if (isRedirecting) return

    // Set redirecting state
    setIsRedirecting(true)

    // Create a new session and redirect to it
    try {
      const newSessionId = createNewChat()
      console.log(`Created new chat session with ID: ${newSessionId}`)
      router.push(`/enterprise/chat/${newSessionId}`)
    } catch (error) {
      console.error('Error creating new chat session:', error)
      setIsRedirecting(false)
    }
  }, [createNewChat, router, isRedirecting])

  // Show loading state while creating a new session
  return (
    <div className="flex-1 flex items-center justify-center">
      <Loader2 className="h-8 w-8 animate-spin text-primary" />
      <span className="ml-2 text-muted-foreground">Creating new chat session...</span>
    </div>
  )
}

export default ChatPage