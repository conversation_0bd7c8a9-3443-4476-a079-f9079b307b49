"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { UsersRound, Search } from "lucide-react";
import { toast } from "sonner";
import { ViewToggle } from "@/components/view-toggle";
import { Pagination } from "@/components/pagination";
import { TeamCardView } from "./_components/team-card-view";
import { TeamTableView } from "./_components/team-table-view";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { fetchTeams, Team, deleteTeam } from "@/store/slices/teamSlice";
import { useAuth } from "@clerk/nextjs";
import axios from "axios"; // Keep this if still needed for the 'getintegration' call, though it seems unused
import { teamPermissions, checkPermissions, integrationPermissions } from "@/utils/ACTION_PERMISSIONS";
import { useBackendUser } from "@/hooks/useBackendUser";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl"; // Import useTranslations

export default function TeamsPage() {
  const t = useTranslations("TeamsPage"); // Initialize useTranslations
  const router = useRouter();
  const { getToken } = useAuth();
  const dispatch = useAppDispatch();
  const { teams, status, error } = useAppSelector((state) => state.teams);
  const { backendUser, loading: backendUserLoading } = useBackendUser();

  // Add permission check at the beginning of the component
  useEffect(() => {
    // Only check permissions after backendUser has loaded
    if (!backendUserLoading && backendUser) {
      const permissions = backendUser?.permissions ?? [];

      // Check if user has at least one team-related permission using the helper function
      const hasAnyTeamPermission = checkPermissions(permissions, [
        teamPermissions.canView,
        teamPermissions.canCreate,
        teamPermissions.canUpdate,
        teamPermissions.canDelete,
        teamPermissions.canAssignUser,
        teamPermissions.canRemoveUser,
        integrationPermissions.canAssignToGroup,
        integrationPermissions.canRemoveFromGroup,
      ]);

      // Redirect if user doesn't have any team-related permissions
      if (!hasAnyTeamPermission) {
        toast.error(t("permissionDeniedToastTitle"), {
          description: t("permissionDeniedToastDescription")
        });
        router.push("/enterprise/dashboard");
      }
    }
  }, [backendUser, backendUserLoading, router, t]); // Add t to dependencies

  const [searchQuery, setSearchQuery] = useState("");
  const [teamToDelete, setTeamToDelete] = useState<Team | null>(null);
  const [view, setView] = useState("card"); // Default to card view
  const [loading, setLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(6);

  // Load teams from Redux store and preferences
  useEffect(() => {
    const loadTeamsAndPreferences = async () => {
      try {
        setLoading(true);

        // Get token from Clerk
        const token = await getToken();
        // The console.log and axios.get for "integrations" seems unrelated and might be vestigial.
        // If 'getintegration' is not used, you can remove it.
        // console.log(token);
        // const getintegration = axios.get(
        //   "https://pe.passisto.com/api/v1/integrations",
        //   {
        //     headers: {
        //       Authorization: `Bearer ${token}`,
        //     },
        //   }
        // );

        // Dispatch the fetchTeams action
        await dispatch(fetchTeams(token!)).unwrap();

        // Load view preference
        const savedView = localStorage.getItem("teamsViewPreference");
        if (savedView) {
          setView(savedView);
        }

        // Load items per page preference
        const savedItemsPerPage = localStorage.getItem("teamsItemsPerPage");
        if (savedItemsPerPage) {
          setItemsPerPage(Number(savedItemsPerPage));
        }
      } catch (error) {
        console.error("Error loading teams:", error);
        toast.error(t("errorLoadingTeamsTitle"), {
          description: t("errorLoadingTeamsDescription"),
        });
      } finally {
        setLoading(false);
      }
    };

    // Only load teams if backendUser has loaded and has permission to view teams
    if (!backendUserLoading && backendUser) {
      const permissions = backendUser.permissions ?? [];
      const hasViewPermission = checkPermissions(permissions, [teamPermissions.canView]);
      if (hasViewPermission) {
        loadTeamsAndPreferences();
      } else {
        setLoading(false); // If no view permission, stop loading state
      }
    }
  }, [dispatch, getToken, backendUser, backendUserLoading, t]); // Add t to dependencies

  // Save view preference to localStorage when it changes
  useEffect(() => {
    localStorage.setItem("teamsViewPreference", view);
  }, [view]);

  // Save items per page preference
  useEffect(() => {
    localStorage.setItem("teamsItemsPerPage", itemsPerPage.toString());
  }, [itemsPerPage]);

  // Reset to first page when search changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery]);

  const filteredTeams = teams.filter(
    (team) =>
      team.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      team.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Calculate pagination
  const totalPages = Math.ceil(filteredTeams.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredTeams.slice(indexOfFirstItem, indexOfLastItem);

  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  const handleDeleteTeam = async () => {
    if (!teamToDelete) return;

    // Prevent deletion of the "Company" team
    if (teamToDelete.name === "Company") {
      toast.error(t("deleteCompanyTeamWarning"), {
        description: t("deleteCompanyTeamWarning"), // Re-using for description too
      });
      setTeamToDelete(null);
      return;
    }

    try {
      setIsSubmitting(true);

      // Get authentication token
      const token = await getToken();

      if (!token) {
        toast.error(t("authenticationRequiredToast"));
        return;
      }

      // Dispatch deleteTeam action
      await dispatch(
        deleteTeam({
          teamId: teamToDelete.id,
          token,
        })
      ).unwrap();

      toast.success(t("teamDeletedToastTitle"), {
        description: t("teamDeletedToastDescription", { teamName: teamToDelete.name }),
      });

      setTeamToDelete(null);
    } catch (error: any) {
      console.error("Error deleting team:", error);
      toast.error(t("errorDeletingTeamTitle"), {
        description: error.message || t("errorDeletingTeamDescription"),
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Only render loading if status is truly loading or backend user is loading
  if (loading || status === "loading" || backendUserLoading) {
    return (
      <div className="container mx-auto py-8 px-4 flex items-center justify-center min-h-[60vh]">
        <div className="flex flex-col items-center">
          <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
          <p className="mt-4 text-muted-foreground">{t("loadingTeams")}</p>
        </div>
      </div>
    );
  }

  // Handle the case where no permission is granted after loading
  if (!backendUser || !checkPermissions(backendUser.permissions || [], [teamPermissions.canView])) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <p className="text-red-500 mb-4">{t("permissionDeniedToastDescription")}</p>
        {/* Potentially add a button to navigate elsewhere if desired */}
      </div>
    );
  }

  // Handle Redux error if it's not a permission issue
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <p className="text-red-500 mb-4">{t("errorLoadingTeamsTitle")}: {error}</p>
        {/* <Button onClick={() => dispatch(fetchTeams(getToken()))}>Retry</Button> */}
      </div>
    );
  }

  return (
    <div className="space-y-6 w-full">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t("teamsTitle")}</h1>
          <p className="text-muted-foreground mt-2">
            {t("teamsDescription")}
          </p>
        </div>
        {teamPermissions.canCreate(backendUser?.permissions ?? []) && (
          <Button asChild>
            <Link href="/enterprise/teams/new">
              <UsersRound className="mr-2 h-4 w-4" />
              {t("createTeamButton")}
            </Link>
          </Button>
        )}
      </div>

      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
        <div className="relative flex-1 w-full">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={t("searchTeamsPlaceholder")}
            className="pl-8 w-full"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <ViewToggle view={view} setView={setView} />
      </div>

      {filteredTeams.length === 0 ? (
        <div className="text-center py-10">
          <p className="text-muted-foreground">
            {t("noTeamsFoundTitle")} {t("noTeamsFoundDescription")}
          </p>
        </div>
      ) : (
        <>
          {view === "card" ? (
            <TeamCardView
              teams={currentItems}
              onDeleteTeam={setTeamToDelete}
              backendUser={backendUser}
            />
          ) : (
            <TeamTableView
              teams={currentItems}
              onDeleteTeam={setTeamToDelete}
              backendUser={backendUser}
            />
          )}
          {filteredTeams.length > 0 && (
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
              itemsPerPage={itemsPerPage}
              onItemsPerPageChange={handleItemsPerPageChange}
              totalItems={filteredTeams.length}
              showingFrom={indexOfFirstItem + 1}
              showingTo={Math.min(indexOfLastItem, filteredTeams.length)}
              viewMode={view}
            />
          )}
        </>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={!!teamToDelete}
        onOpenChange={(open) => !open && setTeamToDelete(null)}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t("deleteTeamDialogTitle")}</DialogTitle>
            <DialogDescription>
              {teamToDelete?.name === "Company" ? (
                <span className="text-destructive">
                  {t("deleteCompanyTeamWarning")}
                </span>
              ) : (
                <>
                  {t("deleteTeamDialogDescription", { teamName: teamToDelete?.name })}
                </>
              )}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setTeamToDelete(null)}
              disabled={isSubmitting}
            >
              {t("cancelButton")}
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteTeam}
              disabled={isSubmitting || teamToDelete?.name === "Company"}
            >
              {isSubmitting ? t("deletingButton") : t("deleteButton")}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}