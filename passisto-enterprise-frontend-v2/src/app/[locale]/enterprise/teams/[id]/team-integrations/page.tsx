"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { toast } from "sonner"
import { ArrowLeft, Database, Globe, FileCode, Server, Loader2 } from "lucide-react"
import Link from "next/link"
import { useAuth } from "@clerk/nextjs"
import { GET_TEAM_INTEGRATIONS, GET_TEAM_WITH_MEMBERS } from "@/utils/routes"
import axiosInstance from "@/config/axios"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { teamPermissions, integrationPermissions } from "@/utils/ACTION_PERMISSIONS";
import { useBackendUser } from "@/hooks/useBackendUser";
import { useTranslations } from "next-intl"

// Define the Integration interface
interface Integration {
  id: string
  name: string
  providerType: "jira" | "ftp" | "web"
  status: "loading" | "completed" | "failed" | "refreshing" | "not updated"
  updateTime: number
  createdBy: string
  createdAt: string
  updatedAt: string
  jira?: any
  ftp?: any
  web?: any
}

// Define the Team interface
interface Team {
  id: string
  name: string
  description: string
}

export default function TeamIntegrationsPage() {
  const t  = useTranslations()
  const { id } = useParams<{ id: string }>()
  const router = useRouter()
  const { getToken } = useAuth()
  const { backendUser, loading: backendUserLoading } = useBackendUser();

  useEffect(() => {
    // Only check permissions after backendUser has loaded
    if (!backendUserLoading && backendUser) {
      // Redirect if user doesn't have permission to view teams
      if (!teamPermissions.canView(backendUser?.permissions ?? [])) {
        toast.error(t('permission-denied'), {
          description: t('you-dont-have-permission-to-view-team-integrations')
        });
        router.push("/enterprise/teams");
      }
    }
  }, [backendUser, backendUserLoading, router]);

  const [team, setTeam] = useState<Team | null>(null)
  const [integrations, setIntegrations] = useState<Integration[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)
        const token = await getToken()
        if (!token) {
          toast.error(t('authentication-required'))
          return
        }

        const headers = {
          Authorization: `Bearer ${token}`
        }

        // Fetch team details
        const teamResponse = await axiosInstance.get(GET_TEAM_WITH_MEMBERS(id as string), {
          headers
        })
        setTeam(teamResponse.data)

        // Fetch team integrations
        const integrationsResponse = await axiosInstance.get(GET_TEAM_INTEGRATIONS(id as string), {
          headers
        })
        setIntegrations(integrationsResponse.data)
      } catch (error: any) {
        console.error("Error fetching team integrations:", error)
        setError(error.message || t('failed-to-load-team-integrations'))
        toast.error(t('failed-to-load-team-integrations'))
      } finally {
        setLoading(false)
      }
    }

    if (id) {
      fetchData()
    }
  }, [id, getToken])

  // Helper function to get the appropriate icon for each integration type
  const getIntegrationIcon = (type: string) => {
    console.log("provider type", type);
    switch (type) {
      case "jira":
        return <FileCode className="h-5 w-5" />
      case "ftp":
        return <Server className="h-5 w-5" />
      case "web":
        return <Globe className="h-5 w-5" />
      default:
        return <Database className="h-5 w-5" />
    }
  }

  // Helper function to get the status badge for each integration
  const getStatusBadge = (status: string) => {
    console.log("status type", status);
    switch (status) {
      case "completed":
        return <Badge variant="success">{t('active-0')}</Badge>
      case "loading":
      case "refreshing":
        return <Badge variant="warning">{t('updating')}</Badge>
      case "failed":
        return <Badge variant="destructive">{t('failed')}</Badge>
      default:
        return <Badge variant="outline">{t('not-updated-0')}</Badge>
    }
  }

  return (
    <div className="container mx-auto py-6 px-4 md:px-6">
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-3">
          <Link href={`/enterprise/teams/${id}/view`}>
            <Button variant="outline" size="icon" className="h-9 w-9 rounded-full">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold tracking-tight">{t('team-integrations')}</h1>
            <p className="text-muted-foreground mt-1">
              {team ? `View all integrations for ${team.name}` : t('loading-team-details')}
            </p>
          </div>
        </div>
        {integrationPermissions.canAssignToGroup(backendUser?.permissions ?? []) && (
          <Link href={`/enterprise/teams/${id}/integrations`}>
            <Button>{t('manage-integrations')}</Button>
          </Link>
        )}
      </div>

      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="overflow-hidden border border-border/40">
              <CardHeader className="pb-2">
                <Skeleton className="h-6 w-3/4 mb-2" />
                <Skeleton className="h-4 w-1/2" />
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-2/3" />
                  <Skeleton className="h-4 w-3/4" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : error ? (
        <div className="flex flex-col justify-center items-center h-64 gap-4">
          <div className="bg-destructive/10 p-3 rounded-full">
            <Database className="h-8 w-8 text-destructive" />
          </div>
          <div className="text-center">
            <h3 className="font-semibold text-lg">{t('error-loading-integrations')}</h3>
            <p className="text-muted-foreground">{error}</p>
          </div>
        </div>
      ) : integrations.length === 0 ? (
        <div className="flex flex-col justify-center items-center h-64 gap-4">
          <div className="bg-muted p-3 rounded-full">
            <Database className="h-8 w-8 text-muted-foreground" />
          </div>
          <div className="text-center">
            <h3 className="font-semibold text-lg">{t('no-integrations-found')}</h3>
            <p className="text-muted-foreground">{t('this-team-doesnt-have-any-integrations-assigned-yet')}</p>
          </div>
          <Link href={`/enterprise/teams/${id}/integrations`}>
            <Button variant="outline">{t('assign-integrations')}</Button>
          </Link>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {integrations.map((integration) => (
            <Card
              key={integration.id}
              className="overflow-hidden border border-border/40 transition-all hover:shadow-md"
            >
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div className="space-y-1">
                    <CardTitle className="text-lg">{integration.name}</CardTitle>
                    <CardDescription>
                      {integration.providerType.charAt(0).toUpperCase() + integration.providerType.slice(1)} {t('integration')}
                    </CardDescription>
                  </div>
                  <div className="flex items-center space-x-2">
                    {getStatusBadge(integration.status)}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center text-sm">
                    {getIntegrationIcon(integration.providerType)}
                    <span className="ml-2">
                      {integration.providerType === "jira" && integration.jira
                        ? `${integration.jira.domain} - ${integration.jira.project}`
                        : integration.providerType === "ftp" && integration.ftp
                        ? `${integration.ftp.server}:${integration.ftp.port}`
                        : integration.providerType === "web" && integration.web
                        ? integration.web.url
                        : t('configuration-details-not-available')}
                    </span>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {t('last-updated')} {new Date(integration.updatedAt).toLocaleString()}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
