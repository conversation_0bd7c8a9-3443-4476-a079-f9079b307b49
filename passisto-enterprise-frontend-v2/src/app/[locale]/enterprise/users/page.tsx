"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@clerk/nextjs";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { UserPlus, Search, Filter, Ban } from "lucide-react";
import { toast } from "sonner";
import { ViewToggle } from "@/components/view-toggle";
import { Pagination } from "@/components/pagination";
import { UserCardView } from "./_components/user-card-view";
import { UserTableView } from "./_components/user-table-view";
import type { User } from "@/store/slices/userSlice";
// Import Redux hooks and actions
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { 
  fetchUsers, 
  deleteUser, 
  toggleUserStatus,
  Team 
} from "@/store/slices/userSlice";
import {
  fetchRolesPermissions,
  Role,
} from "@/store/slices/rolePermissionSlice";
import { fetchTeams } from "@/store/slices/teamSlice";
import { useBackendUser } from '@/hooks/useBackendUser';
// Import user permissions helper
import { checkPermissions, integrationPermissions, userPermissions } from "@/utils/ACTION_PERMISSIONS";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";

export default function UsersPage() {
  const t  = useTranslations()
  const { getToken } = useAuth();
  const router=useRouter();
  const dispatch = useAppDispatch();
  const { users, status, error } = useAppSelector((state) => state.users);
  const { roles, loading: rolesLoading } = useAppSelector(
    (state) => state.rolePermission
  );
  const { teams: availableTeams } = useAppSelector((state) => state.teams);
  const { backendUser, loading: backendUserLoading } = useBackendUser();
  
  const [searchQuery, setSearchQuery] = useState("");
  const [userToDelete, setUserToDelete] = useState<User | null>(null);
  const [roleFilter, setRoleFilter] = useState("all");
  const [teamFilter, setTeamFilter] = useState("all");
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [view, setView] = useState("card"); // Default to card view
  const [loading, setLoading] = useState(true);
  
  

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(6); // Default to 6 for card view

  
  useEffect(() => {
  // Only check permissions after backendUser has loaded
  if (!backendUserLoading && backendUser) {
    const permissions = backendUser?.permissions ?? [];

    // Check if user has at least one user-related permission using the helper function
    const hasAnyUserPermission = checkPermissions(permissions, [
      userPermissions.canView,
      userPermissions.canCreate,
      userPermissions.canUpdate,
      userPermissions.canDelete,
      userPermissions.canInvite,
      userPermissions.canToggleStatus,
      userPermissions.canCompleteProfile
    ]);

    // Redirect if user doesn't have any user-related permissions
    if (!hasAnyUserPermission) {
      toast.error(t('permission-denied-1'), {
        description: t('you-dont-have-permission-to-access-the-users-page')
      });
      router.push("/enterprise/dashboard");
    }
  }
}, [backendUser, backendUserLoading, router]);



  // Load view preference from localStorage on component mount
  useEffect(() => {
    const savedView = localStorage.getItem("usersViewPreference");
    if (savedView) {
      setView(savedView);
      // Set items per page based on view
      setItemsPerPage(savedView === "card" ? 6 : 8);
    }
  }, []);

  // Update items per page when view changes
  useEffect(() => {
    setItemsPerPage(view === "card" ? 6 : 8);
    // Reset to first page when changing view to avoid empty pages
    setCurrentPage(1);
  }, [view]);

  // Fetch users from Redux store
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const token = await getToken();
        await dispatch(fetchUsers(token!)).unwrap();
        await dispatch(fetchRolesPermissions(token!));
        await dispatch(fetchTeams(token!));
      } catch (err) {
        console.error("Error fetching users:", err);
        toast.error(t('error-loading-users'));
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [dispatch, getToken]);

  // Save view preference to localStorage when it changes
  useEffect(() => {
    localStorage.setItem("usersViewPreference", view);
  }, [view]);

  // Filter users based on search query and filters
  const filteredUsers = Array.isArray(users)
    ? users.filter((user) => {
        const matchesSearch =
          (user.firstName?.toLowerCase() || "").includes(
            searchQuery.toLowerCase()
          ) ||
          (user.lastName?.toLowerCase() || "").includes(
            searchQuery.toLowerCase()
          ) ||
          (user.email?.toLowerCase() || "").includes(searchQuery.toLowerCase());

        const matchesRoleFilter =
          roleFilter === "all" ||
          (user.roles &&
            user.roles.some((role: Role) =>
              role.name
                .toLocaleLowerCase()
                .includes(roleFilter.toLocaleLowerCase())
            ));

        const matchesTeamFilter =
          teamFilter === "all" ||
          (user.teams &&
            user.teams.some((team: Team) =>
              team.name
                .toLocaleLowerCase()
                .includes(teamFilter.toLocaleLowerCase())
            ));

        return matchesSearch && matchesRoleFilter && matchesTeamFilter;
      })
    : [];

  // Pagination logic
  const totalPages = Math.ceil(filteredUsers.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredUsers.slice(indexOfFirstItem, indexOfLastItem);

  // Event handlers
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1);
  };

  const handleDeleteUser = async () => {
    if (!userToDelete) return;

    try {
      const token = await getToken();
      await dispatch(deleteUser({ userId: userToDelete.id, token: token! }))
        .unwrap();
      
      toast.success(t('user-deleted'), {
        description: `${userToDelete.firstName} ${userToDelete.lastName} has been removed.`,
      });
      
      setUserToDelete(null);
    } catch (err) {
      console.error("Error deleting user:", err);
      toast.error(t('error-deleting-user'));
    }
  };

  // Ajoutez un état pour gérer l'utilisateur à bannir
  const [userToToggleStatus, setUserToToggleStatus] = useState<User | null>(null);

  // Ajoutez une fonction pour gérer le bannissement
  const handleToggleUserStatus = async () => {
    if (!userToToggleStatus) return;
    
    try {
      const token = await getToken();
      await dispatch(toggleUserStatus({ 
        userId: userToToggleStatus.id, 
        token: token! 
      })).unwrap();
      
      const statusAction = userToToggleStatus.isActive ? 'banned' : 'activated';
      
      toast.success(`User ${statusAction}`, {
        description: `${userToToggleStatus.firstName} ${userToToggleStatus.lastName} has been ${statusAction}.`,
      });
      await dispatch(fetchUsers(token!));
      setUserToToggleStatus(null);
    } catch (err) {
      console.error("Error toggling user status:", err);
      toast.error(t('error-updating-user-status'));
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto py-8 px-4 flex items-center justify-center min-h-[60vh]">
        <div className="flex flex-col items-center">
          <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
          <p className="mt-4 text-muted-foreground">{t('loading-users')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{t('users-0')}</h1>
            <p className="text-muted-foreground mt-2">
              {t('manage-your-organizations-users')}
            </p>
          </div>
          {userPermissions.canCreate(backendUser?.permissions || []) && (
            <Button asChild>
              <Link href="/enterprise/users/new">
                <UserPlus className="mr-2 h-4 w-4" />
                {t('add-user')}
              </Link>
            </Button>
          )}
        </div>

        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
          <div className="relative flex-1 w-full">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={t('search-users')}
              className="pl-8 w-full"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          <div className="flex gap-2">
            <ViewToggle view={view} setView={setView} />

            <Button
              variant="outline"
              size="icon"
              onClick={() => setIsFilterOpen(!isFilterOpen)}
              className="shrink-0"
            >
              <Filter className="h-4 w-4" />
              <span className="sr-only">{t('filter')}</span>
            </Button>
          </div>
        </div>

        {isFilterOpen && (
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium mb-1 block">
                {t('filter-by-role')}
              </label>
              <Select value={roleFilter} onValueChange={setRoleFilter}>
                <SelectTrigger>
                  <SelectValue placeholder={t('select-role')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('all-roles')}</SelectItem>
                  {roles.map((role) => (
                    <SelectItem key={role.id} value={role.name}>
                      {role.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium mb-1 block">
                {t('filter-by-team')}
              </label>
              <Select value={teamFilter} onValueChange={setTeamFilter}>
                <SelectTrigger>
                  <SelectValue placeholder={t('select-team')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('all-teams')}</SelectItem>
                  {availableTeams.map((team) => (
                    <SelectItem key={team.id} value={team.name}>
                      {team.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        )}

        {view === "card" ? (
          <UserCardView 
            users={currentItems} 
            onDeleteUser={setUserToDelete}
            onToggleStatus={setUserToToggleStatus} 
            backendUser={backendUser!}
          />
        ) : (
          <UserTableView 
            users={currentItems} 
            onDeleteUser={setUserToDelete}
            onToggleStatus={setUserToToggleStatus}
            backendUser={backendUser!}
          />
        )}

        {filteredUsers.length > 0 && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            totalItems={filteredUsers.length}
            showingFrom={indexOfFirstItem + 1}
            showingTo={Math.min(itemsPerPage)}
            viewMode={view} // Pass the current view to Pagination
          />
        )}

        {/* Delete Confirmation Dialog */}
        <Dialog
          open={!!userToDelete}
          onOpenChange={(open) => !open && setUserToDelete(null)}
        >
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{t('delete-user')}</DialogTitle>
              <DialogDescription>
                {t('are-you-sure-you-want-to-delete')} {userToDelete?.email}{t('this-action-cannot-be-undone-0')}
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button variant="outline" onClick={() => setUserToDelete(null)}>
                {t('cancel')}
              </Button>
              <Button variant="destructive" onClick={handleDeleteUser}>
                {t('delete')}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Ban Confirmation Dialog */}
        <Dialog 
          open={!!userToToggleStatus} 
          onOpenChange={(open) => !open && setUserToToggleStatus(null)}
        >
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {userToToggleStatus?.isActive ? t('ban-user') : t('activate-user')}
              </DialogTitle>
              <DialogDescription>
                {userToToggleStatus?.isActive 
                  ? `Are you sure you want to ban ${userToToggleStatus?.firstName} ${userToToggleStatus?.lastName}? This will revoke their access to the system.`
                  : `Are you sure you want to activate ${userToToggleStatus?.firstName} ${userToToggleStatus?.lastName}? This will restore their access to the system.`
                }
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button variant="outline" onClick={() => setUserToToggleStatus(null)}>
                {t('cancel')}
              </Button>
              <Button 
                variant={userToToggleStatus?.isActive ? "destructive" : "default"}
                onClick={handleToggleUserStatus}
              >
                {userToToggleStatus?.isActive ? t('ban-user') : t('activate-user')}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}
