"use client";

import { useState, useEffect, useCallback } from "react";
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { toast } from "sonner";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import {
  fetchRolesPermissions,
  Role,
  Permission,
  PermissionWithSource,
} from "@/store/slices/rolePermissionSlice";
import { fetchTeams } from "@/store/slices/teamSlice";
import { useAuth } from "@clerk/nextjs";
import { Tabs, Tabs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import { Separator } from "@/components/ui/separator";
import {
  Popover,
  Popover<PERSON>ontent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";
import {
  ChevronDown,
  Shield,
  Plus,
  Info,
} from "lucide-react";
import { addUser, User, Team } from "@/store/slices/userSlice";
import {
  formatPermissionName,
  getScopeIcon,
  getScopeBadgeColor,
  getScopeLabel,
  groupInheritedPermissionsByCategory,
  getValidScopesForPermission,
  computeInheritedPermissions,
  computeEffectivePermissions,
} from "../_lib/utils";
import { renderScopeSelector } from "../_lib/scope-selectors";

import { userPermissions } from "@/utils/ACTION_PERMISSIONS";
import { useBackendUser } from '@/hooks/useBackendUser';
import { useTranslations } from "next-intl"; // Import useTranslations

export default function NewUserPage() {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { getToken } = useAuth();
  const { backendUser, loading: backendUserLoading } = useBackendUser();
  const t = useTranslations("UserCreation"); // Initialize useTranslations

  // Get roles and permissions from Redux store
  const {
    roles,
    permissionsByCategory,
    scopes,
    loading: rolesLoading,
  } = useAppSelector((state) => state.rolePermission);

  // Get teams from Redux store
  const {
    teams: availableTeams,
    status: teamsStatus,
    error: teamsError,
  } = useAppSelector((state) => state.teams);

  const [formData, setFormData] = useState<User>({
    id: "",
    firstName: "",
    lastName: "",
    email: "",
    roles: [],
    teams: [],
    extraPermissions: [],
    revokedPermissions: [],
    permissionsCount: null,
    isActive: null,
    createdAt: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [inheritedPermissions, setInheritedPermissions] = useState<
    PermissionWithSource[]
  >([]);
  const [effectivePermissions, setEffectivePermissions] = useState<
    Permission[]
  >([]);
  const [formErrors, setFormErrors] = useState<{
    firstName?: string;
    lastName?: string;
    email?: string;
    roles?: string;
    emailExists?: string
  }>({});

  useEffect(() => {
    // Only check permissions after backendUser has loaded
    if (!backendUserLoading && backendUser) {
      // Redirect if user doesn't have permission to create users
      if (!userPermissions.canCreate(backendUser?.permissions ?? [])) {
        toast.error(t('toasts.permissionDeniedTitle'), {
          description: t('toasts.permissionDeniedDescription')
        });
        router.push("/enterprise/users");
      }
    }
  }, [backendUser, backendUserLoading, router, t]);

  // Fetch roles, permissions and teams on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        const token = await getToken();

        await dispatch(fetchRolesPermissions(token!)).unwrap();
        await dispatch(fetchTeams(token!)).unwrap();
      } catch (err) {
        console.error("Error fetching data:", err);
        toast.error(t('toasts.dataError'));
      }
    };

    fetchData();
  }, [dispatch, getToken, t]);

  useEffect(() => {
    setEffectivePermissions(calculateEffectivePermissions());
    setInheritedPermissions(calculateInheritedPermissions());
  }, [
    formData?.roles,
    formData?.teams,
    formData?.extraPermissions,
    formData?.revokedPermissions,
    roles,
    availableTeams,
    calculateEffectivePermissions, // Added to dependencies
    calculateInheritedPermissions // Added to dependencies
  ]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Handle role selection
  const handleRolesChange = (selectedRoles: Role[]) => {
    setFormData((prev) => ({
      ...prev,
      roles: selectedRoles,
    }));
  };

  const handleExtraPermissionChange = (
    id: string,
    permission: string,
    scopeType: string,
    scopeId: string
  ) => {
    console.log(scopeId);
    // If permission with this scopeType is already in extraPermissions, remove it
    if (
      formData?.extraPermissions.some(
        (p) =>
          p.action === permission &&
          p.scopeType === scopeType &&
          (scopeId === null || p.scopeId === scopeId)
      )
    ) {
      setFormData((prev) => ({
        ...prev,
        extraPermissions: prev.extraPermissions.filter(
          (p) =>
            !(
              p.action === permission &&
              p.scopeType === scopeType &&
              (scopeId === null || p.scopeId === scopeId)
            )
        ),
      }));
    }
    // Otherwise add it
    else {
      const newPermission: Permission = {
        id: id,
        action: permission,
        scopeType,
        scopeId: scopeId!,
        // ...(scopeId && { scopeId }),
      };

      setFormData((prev) => ({
        ...prev,
        extraPermissions: [...prev.extraPermissions, newPermission],
        // If it was revoked with the same scopeType, remove from revoked permissions
        revokedPermissions: prev.revokedPermissions.filter(
          (p) =>
            !(
              p.action === permission &&
              p.scopeType === scopeType &&
              (scopeId === null || p.scopeId === scopeId)
            )
        ),
      }));
    }
  };

  const handleRevokedPermissionChange = (
    permission: string,
    scopeType: string,
    scopeId: string
  ) => {
    // If permission with this scopeType is already in revokedPermissions, remove it
    if (
      formData?.revokedPermissions.some(
        (p) =>
          p.action === permission &&
          p.scopeType === scopeType &&
          (scopeId === null || p.scopeId === scopeId)
      )
    ) {
      setFormData((prev) => ({
        ...prev,
        revokedPermissions: prev.revokedPermissions.filter(
          (p) =>
            !(
              p.action === permission &&
              p.scopeType === scopeType &&
              (scopeId === null || p.scopeId === scopeId)
            )
        ),
      }));
    }
    // Otherwise add it
    else {
      // Find the permission object to get its ID
      const permissionObj = permissionsByCategory
        .flatMap((category) => category.permissions)
        .find((p) => p.action === permission);

      const newRevocation: Permission = {
        id: permissionObj?.id!,
        action: permission,
        scopeType: scopeType,
        scopeId: scopeId!, // Use the team ID directly, not the name
      };

      setFormData((prev) => ({
        ...prev,
        revokedPermissions: [...prev.revokedPermissions, newRevocation],
        // If it was extra with the same scopeType, remove from extra permissions
        extraPermissions: prev.extraPermissions.filter(
          (p) =>
            !(
              p.action === permission &&
              p.scopeType === scopeType &&
              (scopeId === null || p.scopeId === scopeId)
            )
        ),
      }));
    }
  };

  // Calculate permissions inherited from roles and teams
  const calculateInheritedPermissions = useCallback((): PermissionWithSource[] => {
    return computeInheritedPermissions(roles, availableTeams, formData);
  }, [roles, formData?.roles, availableTeams, formData?.teams]);

  // Calculate effective permissions
  const calculateEffectivePermissions = useCallback((): Permission[] => {
    return computeEffectivePermissions(roles, availableTeams, formData, permissionsByCategory);
  }, [formData, roles, formData?.roles, availableTeams, formData?.teams, permissionsByCategory]);


  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Form submitted");

    setIsSubmitting(true);
    setFormErrors({});

    const errors: {
      firstName?: string;
      lastName?: string;
      email?: string;
      roles?: string;
    } = {};

    // Client-side validation
    if (!formData.firstName?.trim()) {
      errors.firstName = t('formErrors.firstNameRequired');
    }

    if (!formData.lastName?.trim()) {
      errors.lastName = t('formErrors.lastNameRequired');
    }

    if (!formData.email?.trim()) {
      errors.email = t('formErrors.emailRequired');
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = t('formErrors.emailInvalid');
    }

    // Validate that at least one role is selected
    if (!formData.roles || formData.roles.length === 0) {
      errors.roles = t('formErrors.roleRequired');
    }

    console.log("Validation errors:", errors);

    if (Object.keys(errors).length > 0) {
      console.log("Setting form errors:", errors);
      setFormErrors(errors);
      setIsSubmitting(false);
      return;
    }

    try {
      // Get authentication token
      const token = await getToken();

      if (!token) {
        toast.error(t('toasts.authRequired'));
        setIsSubmitting(false);
        return;
      }

      const userData = {
        email: formData.email,
        firstName: formData.firstName,
        lastName: formData.lastName,
        groups: formData.teams.map((team) => team.id),
        roles: formData.roles.map((role) => role.id),
        extraPermissions: formData.extraPermissions.map((perm) => {
          // Find the permission object if ID is not available
          let permissionId = perm.id;
          if (!permissionId) {
            const permissionObj = permissionsByCategory
              .flatMap((category) => category.permissions)
              .find((p) => p.action === perm.action);
            permissionId = permissionObj?.id || perm.action;
          }

          // Format scopeId based on scopeType
          let scopeId = "global";
          if (perm.scopeType === "TEAM" && perm.scopeId) {
            scopeId = `team-id:${perm.scopeId.replace("team-id:", "")}`;
          } else if (perm.scopeType === "PROJECT" && perm.scopeId) {
            scopeId = `project-id:${perm.scopeId}`;
          }

          return {
            permissionId: permissionId,
            scopeType: perm.scopeType,
            scopeId: scopeId,
          };
        }),
        revokedPermissions: formData.revokedPermissions.map((perm) => {
          // Find the permission object if ID is not available
          let permissionId = perm.id;
          if (!permissionId) {
            const permissionObj = permissionsByCategory
              .flatMap((category) => category.permissions)
              .find((p) => p.action === perm.action);
            permissionId = permissionObj?.id || perm.action;
          }

          // Format scopeId based on scopeType
          let scopeId = "global";
          if (perm.scopeType === "TEAM" && perm.scopeId) {
            scopeId = `team-id:${perm.scopeId.replace("team-id:", "")}`;
          } else if (perm.scopeType === "PROJECT" && perm.scopeId) {
            scopeId = `project-id:${perm.scopeId}`;
          }

          return {
            permissionId: permissionId,
            scopeType: perm.scopeType,
            scopeId: scopeId,
          };
        }),
      };

      // Dispatch addUser action with the user data and token
      await dispatch(addUser({ userData, token })).unwrap();

      toast.success(t('toasts.userCreatedSuccess'));
      router.push("/enterprise/users");
    } catch (error: any) {
      console.error("Error creating user:", error);
      console.error("Error message:", error.message);
      console.error("Error response:", error.response?.data);

      // Check for user already exists error
      if (error.response?.status === 409) {
        setFormErrors({ email: t('formErrors.emailConflict') });
      } else {
        // Set a form error for any other error
        setFormErrors({ email: t('formErrors.emailConflict') }); // Fallback for other errors
        // Also show the toast for additional visibility
        toast.error(t('toasts.creationFailedTitle'), {
          description: error.message || t('toasts.creationFailedDescription'),
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="w-full p-4">
      <Card>
        <CardHeader>
          <CardTitle>{t('cardTitle')}</CardTitle>
          <CardDescription>
            {t('cardDescription')}
          </CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-4">


            {/* Alert for form errors - Correction */}
            {Object.keys(formErrors).length > 0 && (
              <div className="bg-destructive/15 text-destructive px-4 py-3 rounded-md mb-4 border border-destructive/30">
                <h4 className="font-medium mb-1">
                  {formErrors.email
                    ? t('formErrors.userExists')
                    : (formErrors.firstName || formErrors.lastName || formErrors.email || formErrors.roles)
                      ? t('formErrors.fillInFields')
                      : t('formErrors.error')}
                </h4>
                <ul className="list-disc list-inside text-sm">
                  {formErrors.firstName && <li>{formErrors.firstName}</li>}
                  {formErrors.lastName && <li>{formErrors.lastName}</li>}
                  {formErrors.email && <li>{formErrors.email}</li>}
                  {formErrors.roles && <li>{formErrors.roles}</li>}
                </ul>
              </div>
            )}

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName">{t('firstNameLabel')}</Label>
                <Input
                  id="firstName"
                  name="firstName"
                  placeholder={t('firstNamePlaceholder')}
                  value={formData.firstName}
                  onChange={handleChange}
                  className={formErrors.firstName ? "border-red-500" : ""}

                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="lastName">{t('lastNameLabel')}</Label>
                <Input
                  id="lastName"
                  name="lastName"
                  placeholder={t('lastNamePlaceholder')}
                  value={formData.lastName}
                  onChange={handleChange}
                  className={formErrors.lastName ? "border-red-500" : ""}

                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">{t('emailLabel')}</Label>
              <Input
                id="email"
                name="email"
                type="email"
                placeholder={t('emailPlaceholder')}
                value={formData.email}
                onChange={handleChange}
                className={formErrors.email ? "border-red-500" : ""}

              />
            </div>

            <Tabs defaultValue="roles" className="mt-6">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="roles">{t('tabs.roles')}</TabsTrigger>
                <TabsTrigger value="teams">{t('tabs.teams')}</TabsTrigger>
                <TabsTrigger value="permissions">
                  {t('tabs.permissions')}
                </TabsTrigger>
              </TabsList>

              <TabsContent value="roles" className="pt-4">
                <div className="space-y-2">
                  <Label>{t('rolesSection.label')}</Label>
                  {rolesLoading ? (
                    <div className="text-center py-4">{t('rolesSection.loading')}</div>
                  ) : (
                    <div className="grid grid-cols-2 gap-2">
                      {roles.map((role) => (
                        <div
                          key={role.id}
                          className="flex items-center space-x-2"
                        >
                          <Checkbox
                            id={`role-${role.id}`}
                            checked={formData?.roles.some(
                              (r) => r.id === role.id
                            )}
                            onCheckedChange={() => {
                              const newRoles = formData?.roles.some(
                                (r) => r.id === role.id
                              )
                                ? formData?.roles.filter(
                                  (r) => r.id !== role.id
                                )
                                : [...formData?.roles, role];
                              handleRolesChange(newRoles);
                            }}
                          />
                          <label
                            htmlFor={`role-${role.id}`}
                            className="text-sm"
                          >
                            {role.name}
                          </label>
                        </div>
                      ))}
                      {roles.length === 0 && (
                        <div className="text-center py-4">
                          {t('rolesSection.noRoles')}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="teams" className="pt-4">
                <div className="space-y-2">
                  <Label>{t('teamsSection.label')}</Label>
                  {teamsStatus === "loading" ? (
                    <div className="text-center py-4">{t('teamsSection.loading')}</div>
                  ) : teamsStatus === "failed" ? (
                    <div className="text-center py-4 text-red-500">
                      {t('teamsSection.failedToLoad', { error: teamsError })}
                    </div>
                  ) : availableTeams.length === 0 ? (
                    <div className="text-center py-4">{t('teamsSection.noTeams')}</div>
                  ) : (
                    <div className="grid grid-cols-2 gap-2">
                      {availableTeams.map((team) => (
                        <div
                          key={team.id}
                          className="flex items-center space-x-2"
                        >
                          <Checkbox
                            id={`team-${team.id}`}
                            checked={
                              team.name === "Company" ||
                              formData?.teams.some((t) => t.name === team.name)
                            }
                            disabled={team.name === "Company"} // Disable checkbox for Company team
                            onCheckedChange={() => {
                              // Skip if it's the Company team
                              if (team.name === "Company") return;

                              const isSelected = formData?.teams.some(
                                (t) => t.id === team.id
                              );
                              const newTeams = isSelected
                                ? formData?.teams.filter(
                                  (t) => t.id !== team.id
                                )
                                : [
                                  ...formData?.teams,
                                  {
                                    ...team,
                                    permissions: team.permissions ?? [], // Ensure it's never undefined
                                  } as Team, // Cast to Team if needed
                                ];

                              setFormData((prev) => ({
                                ...prev,
                                teams: newTeams,
                              }));
                            }}
                          />
                          <label
                            htmlFor={`team-${team.id}`}
                            className={`text-sm ${
                              team.name === "Company" ? "font-medium" : ""
                            }`}
                          >
                            {team.name}
                            {team.name === "Company" && t('teamsSection.companyTeamRequired')}
                            {team.memberCount !== undefined &&
                              team.memberCount > 0 && (
                                <span className="ml-1 text-xs text-gray-500">
                                  ({team.memberCount}{" "}
                                  {t('teamsSection.memberCount', { count: team.memberCount })}
                                  )
                                </span>
                              )}
                          </label>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="permissions" className="pt-4">
                <div className="space-y-6">
                  {/* Inherited Permissions Section */}
                  <div>
                    <div className="flex items-center mb-2">
                      <Info className="h-4 w-4 mr-2 text-blue-500" />
                      <h3 className="text-sm font-medium">
                        {t('inheritedPermissions.title')}
                      </h3>
                    </div>
                    <p className="text-xs text-muted-foreground mb-4">
                      {t('inheritedPermissions.description')}
                    </p>

                    {inheritedPermissions.length === 0 ? (
                      <div className="text-center py-4 border rounded-lg bg-muted/20">
                        <p className="text-muted-foreground">
                          {t('inheritedPermissions.noPermissions')}
                        </p>
                      </div>
                    ) : (
                      Object.entries(
                        groupInheritedPermissionsByCategory(
                          inheritedPermissions,
                          permissionsByCategory
                        )
                      ).map(([category, permissions]) => (
                        <div key={category} className="mb-4">
                          <h4 className="text-sm font-medium mb-2">
                            {category}
                          </h4>
                          <div className="grid grid-cols-1 gap-2 border rounded-md p-3">
                            {permissions.map((permObj) => (
                              <div
                                key={`${permObj.action}-${permObj.scopeType}`}
                                className="flex items-center justify-between"
                              >
                                <div className="flex items-center">
                                  <Checkbox
                                    id={`inherited-${permObj.action}-${permObj.scopeType}`}
                                    checked={true}
                                    disabled={true}
                                  />
                                  <label
                                    htmlFor={`inherited-${permObj.action}-${permObj.scopeType}`}
                                    className="text-sm ml-2"
                                  >
                                    {formatPermissionName(permObj.action)}
                                  </label>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <div
                                    className={`text-xs px-2 py-0.5 rounded-full flex items-center ${getScopeBadgeColor(
                                      permObj.scopeType
                                    )}`}
                                  >
                                    {getScopeIcon(permObj.scopeType)}
                                    <span className="ml-1">
                                      {getScopeLabel(permObj.scopeType)}
                                    </span>
                                  </div>
                                  <Badge variant="outline" className="text-xs">
                                    {permObj.source.type === "role"
                                      ? t('inheritedPermissions.sourceRole', { name: permObj.source.name })
                                      : t('inheritedPermissions.sourceTeam', { name: permObj.source.name })}
                                  </Badge>
                                  <Checkbox
                                    id={`revoke-${permObj.action}-${permObj.scopeType}`}
                                    checked={formData?.revokedPermissions.some(
                                      (p) =>
                                        p.action === permObj.action &&
                                        p.scopeType === permObj.scopeType
                                    )}
                                    onCheckedChange={() =>
                                      handleRevokedPermissionChange(
                                        permObj.action,
                                        permObj.scopeType,
                                        permObj.scopeId!
                                      )
                                    }
                                  />
                                  <label
                                    htmlFor={`revoke-${permObj.action}-${permObj.scopeType}`}
                                    className="text-xs text-destructive"
                                  >
                                    {t('inheritedPermissions.revokeAction')}
                                  </label>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))
                    )}
                  </div>

                  <Separator />

                  {/* Extra Permissions Section */}
                  <div>
                    <h3 className="text-sm font-medium flex items-center mb-2">
                      <Plus className="h-4 w-4 mr-2 text-green-500" />
                      {t('extraPermissions.title')}
                    </h3>
                    <p className="text-xs text-muted-foreground mb-4">
                      {t('extraPermissions.description')}
                    </p>

                    {rolesLoading ? (
                      <div className="text-center py-4">
                        {t('extraPermissions.loading')}
                      </div>
                    ) : (
                      permissionsByCategory.map((category) => {
                        const availablePermissions = category.permissions;

                        if (availablePermissions.length === 0) return null;

                        return (
                          <div key={category.category} className="mb-4">
                            <h4 className="text-sm font-medium mb-2">
                              {category.category}
                            </h4>
                            <div className="grid grid-cols-1 gap-2 border rounded-md p-3">
                              {availablePermissions.map((permission) => {
                                const validScopes = getValidScopesForPermission(
                                  permission.action
                                );

                                return (
                                  <div
                                    key={permission.id}
                                    className="flex flex-col space-y-2"
                                  >
                                    <div className="flex items-center">
                                      <label className="text-sm font-medium">
                                        {formatPermissionName(
                                          permission.action
                                        )}
                                      </label>
                                    </div>
                                    <div className="space-y-4">
                                      {scopes
                                        .filter((scopeType) =>
                                          validScopes.includes(scopeType)
                                        )
                                        .map((scopeType) => {
                                          const needsTarget =
                                            scopeType === "TEAM" ||
                                            scopeType === "PROJECT";

                                          if (needsTarget) {
                                            return (
                                              <Popover
                                                key={`${permission.action}-${scopeType}`}
                                              >
                                                <div className="flex items-center space-x-2 ml-4">
                                                  <Checkbox
                                                    id={`extra-${permission.action}-${scopeType}`}
                                                    checked={formData?.extraPermissions.some(
                                                      (p) =>
                                                        p.action ===
                                                        permission.action &&
                                                        p.scopeType ===
                                                        scopeType
                                                    )}
                                                    onCheckedChange={() => {
                                                      if (
                                                        formData?.extraPermissions.some(
                                                          (p) =>
                                                            p.action ===
                                                            permission.action &&
                                                            p.scopeType ===
                                                            scopeType
                                                        )
                                                      ) {
                                                        // Remove all instances of this permission with this scopeType
                                                        setFormData((prev) => ({
                                                          ...prev,
                                                          extraPermissions: prev.extraPermissions.filter(
                                                            (p) =>
                                                              !(
                                                                p.action ===
                                                                permission.action &&
                                                                p.scopeType ===
                                                                scopeType
                                                              )
                                                          ),
                                                        }));
                                                      } else {
                                                        // This assumes you want to add a permission with a default or no specific scopeId when the checkbox is checked initially.
                                                        // If specific scopeId is required on check, you'd need to adjust this logic,
                                                        // perhaps by making the popover open automatically or setting a default scopeId.
                                                        handleExtraPermissionChange(
                                                          permission.id,
                                                          permission.action,
                                                          scopeType,
                                                          "global" // Or some other default if applicable
                                                        );
                                                      }
                                                    }}
                                                  />
                                                  <label
                                                    htmlFor={`extra-${permission.action}-${scopeType}`}
                                                    className="text-sm"
                                                  >
                                                    {getScopeLabel(scopeType)}
                                                  </label>
                                                  <PopoverTrigger asChild>
                                                    <Button
                                                      variant="outline"
                                                      size="sm"
                                                      className="ml-2"
                                                    >
                                                      Select {getScopeLabel(scopeType)}
                                                      <ChevronDown className="ml-2 h-4 w-4" />
                                                    </Button>
                                                  </PopoverTrigger>
                                                  <PopoverContent className="w-64 p-2">
                                                    {renderScopeSelector(
                                                      scopeType,
                                                      availableTeams, // Pass availableTeams
                                                      // If there were projects, you'd pass them here
                                                      null, // projects
                                                      formData?.extraPermissions,
                                                      (selectedScopeId: string | null) => {
                                                        // Handle selection from popover
                                                        // This means the user has specifically chosen a scopeId
                                                        if (selectedScopeId !== null) {
                                                          handleExtraPermissionChange(
                                                            permission.id,
                                                            permission.action,
                                                            scopeType,
                                                            selectedScopeId
                                                          );
                                                        } else {
                                                          // If selectedScopeId is null, it means 'global' or 'all' was selected
                                                          handleExtraPermissionChange(
                                                            permission.id,
                                                            permission.action,
                                                            scopeType,
                                                            "global"
                                                          );
                                                        }
                                                      },
                                                      permission.action
                                                    )}
                                                  </PopoverContent>
                                                </div>
                                              </Popover>
                                            );
                                          } else {
                                            // For scopes that don't need target selection (e.g., GLOBAL)
                                            return (
                                              <div
                                                key={`${permission.action}-${scopeType}`}
                                                className="flex items-center space-x-2 ml-4"
                                              >
                                                <Checkbox
                                                  id={`extra-${permission.action}-${scopeType}`}
                                                  checked={formData?.extraPermissions.some(
                                                    (p) =>
                                                      p.action ===
                                                      permission.action &&
                                                      p.scopeType === scopeType
                                                  )}
                                                  onCheckedChange={() =>
                                                    handleExtraPermissionChange(
                                                      permission.id,
                                                      permission.action,
                                                      scopeType,
                                                      "global" // Default for GLOBAL scope
                                                    )
                                                  }
                                                />
                                                <label
                                                  htmlFor={`extra-${permission.action}-${scopeType}`}
                                                  className="text-sm"
                                                >
                                                  {getScopeLabel(scopeType)}
                                                </label>
                                              </div>
                                            );
                                          }
                                        })}
                                    </div>
                                  </div>
                                );
                              })}
                            </div>
                          </div>
                        );
                      })
                    )}
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
          <CardFooter className="flex justify-end gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push("/enterprise/users")}
            >
              {t('cancelButton')}
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Creating..." : t('submitButton')}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}