"use client"

import { useEffect, useState, Suspense } from "react"
import { useSearchParams } from "next/navigation"
import { WorkflowRunsDashboard } from "@/components/workflows/dashboard/workflow-runs-dashboard"
import { workflowRunApi } from "@/services/workflowRunApi"
import { toast } from "@/hooks/use-toast"
import { type WorkflowRun } from "@/components/workflows/workflow-runs-table"
import { ScrollArea } from "@/components/ui/scroll-area"

// Create a wrapper component that uses searchParams
function WorkflowRunsContent() {
  const searchParams = useSearchParams()
  const [initialRun, setInitialRun] = useState<WorkflowRun | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  // Check for id parameter in URL
  useEffect(() => {
    const runId = searchParams.get('id')
    if (runId) {
      loadWorkflowRun(runId)
    }
  }, [searchParams])

  // Load workflow run by ID
  const loadWorkflowRun = async (runId: string) => {
    try {
      setIsLoading(true)
      const run = await workflowRunApi.getWorkflowRunWithDetails(runId)
      setInitialRun(run)
    } catch (error) {
      console.error("Error loading workflow run:", error)
      toast({
        title: "Error",
        description: "Failed to load workflow run. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <ScrollArea className="h-[calc(100vh-64px)]">
      <div className="container mx-auto py-6">
        <WorkflowRunsDashboard initialRun={initialRun} isInitiallyLoading={isLoading} />
      </div>
    </ScrollArea>
  )
}

// Main component that uses Suspense
export default function WorkflowRunsPage() {
  return (
    <Suspense fallback={
      <ScrollArea className="h-[calc(100vh-64px)]">
        <div className="container mx-auto py-6 flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </ScrollArea>
    }>
      <WorkflowRunsContent />
    </Suspense>
  )
}