"use client"

import { UserTasks } from "@/components/workflows/user-tasks"
import { DashboardShell } from "@/components/workflows/dashboard-shell"
import { DashboardHeader } from "@/components/workflows/dashboard-header"
import { ScrollArea } from "@/components/ui/scroll-area"

export default function MyTasksPage() {
  return (
    <DashboardShell>
      <ScrollArea className="h-[calc(100vh-120px)]">
        <div className="p-6">
          <DashboardHeader
            heading="My Tasks"
            text="View and complete tasks assigned to you from various workflows."
          />
          <div className="grid gap-8">
            <UserTasks />
          </div>
        </div>
      </ScrollArea>
    </DashboardShell>
  )
}
