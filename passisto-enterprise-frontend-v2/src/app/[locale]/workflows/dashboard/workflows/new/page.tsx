"use client"

import WorkflowDesigner from "@/components/workflows/workflow-designer"
import { DashboardShell } from "@/components/workflows/dashboard-shell"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import { useRouter } from "next/navigation"
import { toast } from "@/hooks/use-toast"

export default function NewWorkflowPage() {
  const router = useRouter()

  const handleWorkflowSaved = () => {
    toast({
      title: "Success",
      description: "Workflow created successfully.",
    })
    router.push("/workflows/dashboard/workflows")
  }

  return (
    <DashboardShell className="p-0 overflow-hidden">
      <div className="border-b p-4">
        <Button variant="ghost" size="sm" onClick={() => router.push("/workflows/dashboard/workflows")} className="mb-4">
          <ArrowLeft className="h-4 w-4 mr-2" /> Back to Workflows
        </Button>
      </div>
      <div className="flex-1 h-[calc(100vh-64px)] overflow-hidden">
        <WorkflowDesigner
          currentWorkflow={null}
          onWorkflowSaved={handleWorkflowSaved}
        />
      </div>
    </DashboardShell>
  )
}
