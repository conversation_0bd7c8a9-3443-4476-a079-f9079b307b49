"use client"

import { useState, useEffect } from "react"
import { usePara<PERSON> } from "next/navigation"
import WorkflowDesigner from "@/components/workflows/workflow-designer"
import { type Workflow } from "@/components/workflows/workflows-table"
import { DashboardShell } from "@/components/workflows/dashboard-shell"
import { workflowApi } from "@/services/workflowApi"
import { toast } from "@/hooks/use-toast"

export default function WorkflowEditorPage() {
  const params = useParams()
  const [workflow, setWorkflow] = useState<Workflow | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const workflowId = params?.id as string
  const isNewWorkflow = workflowId === "new"

  useEffect(() => {
    if (!isNewWorkflow) {
      loadWorkflow()
    } else {
      setIsLoading(false)
    }
  }, [workflowId])

  const loadWorkflow = async () => {
    try {
      setIsLoading(true)
      const data = await workflowApi.getWorkflow(workflowId)
      setWorkflow(data)
    } catch (error) {
      console.error("Error loading workflow:", error)
      toast({
        title: "Error",
        description: "Failed to load workflow. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleWorkflowSaved = (savedWorkflow: Workflow) => {
    setWorkflow(savedWorkflow)
    toast({
      title: "Success",
      description: "Workflow saved successfully.",
    })
  }

  return (
    <DashboardShell className="p-0 m-0 overflow-hidden">
      <div className="flex-1 h-[calc(100vh-0px)] overflow-hidden">
        {isLoading ? (
          <div className="flex items-center justify-center h-full">
            <p>Loading workflow...</p>
          </div>
        ) : (
          <WorkflowDesigner
            currentWorkflow={workflow}
            onWorkflowSaved={handleWorkflowSaved}
          />
        )}
      </div>
    </DashboardShell>
  )
}
