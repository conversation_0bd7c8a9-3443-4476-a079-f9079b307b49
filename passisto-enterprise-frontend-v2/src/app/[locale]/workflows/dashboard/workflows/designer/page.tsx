"use client";

import { useState, useEffect, Suspense } from "react";
import { useSearch<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import WorkflowDesigner from "@/components/workflows/workflow-designer";
import { workflowApi } from "@/services/workflowApi";
import { Workflow } from "@/components/workflows/workflows-table";
import { toast } from "@/hooks/use-toast";
import { DashboardShell } from "@/components/workflows/dashboard-shell";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";

// Create a wrapper component that uses searchParams
function WorkflowDesignerContent() {
  const searchParams = useSearchParams();
  const workflowId = searchParams.get("id");
  const router = useRouter();

  const [currentWorkflow, setCurrentWorkflow] = useState<Workflow | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Load workflow data when the component mounts or workflowId changes
  useEffect(() => {
    async function loadWorkflow() {
      if (!workflowId) {
        // If no ID is provided, we're creating a new workflow
        setCurrentWorkflow(null);
        return;
      }

      try {
        setIsLoading(true);
        const workflow = await workflowApi.getWorkflow(workflowId);
        setCurrentWorkflow(workflow);
      } catch (error: any) {
        console.error("Error loading workflow:", error);
        toast({
          title: "Error loading workflow",
          description: error.message ?? "Failed to load workflow",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    }

    loadWorkflow();
  }, [workflowId]);

  // Handle workflow saved event
  const handleWorkflowSaved = (savedWorkflow: Workflow) => {
    setCurrentWorkflow(savedWorkflow);

    toast({
      title: "Success",
      description: "Workflow saved successfully.",
    });

    // Update URL with the workflow ID if it's a new workflow
    if (!workflowId && savedWorkflow.id) {
      // Use history.replaceState to update the URL without causing a navigation
      const url = new URL(window.location.href);
      url.searchParams.set("id", savedWorkflow.id);
      window.history.replaceState({}, "", url.toString());
    }
  };

  return (
    <DashboardShell className="p-0 overflow-hidden">
      <div className="border-b p-2 flex items-center">
        <Button variant="ghost" size="sm" onClick={() => router.push("/workflows/dashboard/workflows")}>
          <ArrowLeft className="h-4 w-4 mr-2" /> Back to Workflows
        </Button>
      </div>
      <div className="flex-1 h-[calc(100vh-48px)] overflow-hidden">
        {isLoading ? (
          <div className="flex items-center justify-center h-full">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : (
          <WorkflowDesigner
            currentWorkflow={currentWorkflow}
            onWorkflowSaved={handleWorkflowSaved}
          />
        )}
      </div>
    </DashboardShell>
  );
}

// Main component that uses Suspense
export default function WorkflowDesignerPage() {
  return (
    <Suspense fallback={
      <DashboardShell className="p-0 overflow-hidden">
        <div className="flex-1 h-[calc(100vh-48px)] overflow-hidden">
          <div className="flex items-center justify-center h-full">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        </div>
      </DashboardShell>
    }>
      <WorkflowDesignerContent />
    </Suspense>
  );
}
