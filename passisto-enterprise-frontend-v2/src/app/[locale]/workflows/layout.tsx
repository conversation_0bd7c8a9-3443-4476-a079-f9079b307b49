import type { Metadata } from 'next'
import './globals.css'
import Provider from '@/app/[locale]/provider'


export const metadata: Metadata = {
  title: 'v0 App',
  description: 'Created with v0',
  generator: 'v0.dev',
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" className="h-full">
      <body className="h-full">
        <Provider>
          {children}
        </Provider>
      </body>
    </html>
  )
}
