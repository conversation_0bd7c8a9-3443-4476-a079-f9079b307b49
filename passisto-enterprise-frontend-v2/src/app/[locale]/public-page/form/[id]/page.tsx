"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import { FormSubmission } from "@/app/[locale]/enterprise/ai-agents/form-builder/_components/form-submission"
import { StoredForm } from "@/app/[locale]/enterprise/ai-agents/form-builder/_lib/types"
import { useTranslations } from "next-intl"

export default function PublicFormPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const [form, setForm] = useState<StoredForm | null>(null)
  const [loading, setLoading] = useState(true)
  const t = useTranslations()

  useEffect(() => {
    // Load form from localStorage
    try {
      const storedForms = localStorage.getItem("forms")
      if (storedForms) {
        const forms = JSON.parse(storedForms) as StoredForm[]
        const foundForm = forms.find((f) => f.id === params.id)
        setForm(foundForm || null)
      }
    } catch (error) {
      console.error("Error loading form:", error)
    } finally {
      setLoading(false)
    }
  }, [params])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    )
  }

  if (!form) {
    return (
      <div className="min-h-screen p-4 md:p-8 bg-gray-50">
        <div className="max-w-3xl mx-auto text-center">
          <h1 className="text-2xl font-bold mb-4">{t('form-not-found')}</h1>
          <p className="mb-6">{t('the-form-youre-looking-for-doesnt-exist-or-has-been-deleted')}</p>
          <Button onClick={() => router.push("/enterprise/integrations/new")}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('back-to-home')}
          </Button>
        </div>
      </div>
    )
  }

  return (
    <main className="bg-gray-50">
      <div className="w-full mx-auto">
        <FormSubmission form={form} />
      </div>
    </main>
  )
}

