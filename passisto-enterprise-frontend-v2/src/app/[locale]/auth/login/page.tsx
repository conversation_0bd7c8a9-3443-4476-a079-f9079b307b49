import { SignIn } from "@clerk/nextjs";
import Link from "next/link";
import { Loader2Icon } from "lucide-react";
import { useTranslations } from "next-intl";
export default function SignInPage() {
  // const clerkLoaded = useLoaded();
  const t = useTranslations()
  return (
    <main className="w-full flex">
      <div className="relative flex-1 hidden items-center justify-center h-screen bg-gray-900 lg:flex">
        <div className="relative z-10 w-full max-w-md">
          <img src="/logo-white.png" width={300} height={100} />
          <div className=" mt-16 space-y-3">
            <h3 className="text-white text-3xl font-bold">
              {t('start-growing-your-business-quickly')}
            </h3>
            <p className="text-gray-300">
              {t('create-an-account-and-get-access-to-all-features-for-15-days-no-credit-card-required')}
            </p>
            <div className="flex items-center -space-x-2 overflow-hidden">
              <img
                src="https://randomuser.me/api/portraits/women/79.jpg"
                className="w-10 h-10 rounded-full border-2 border-white"
              />
              <img
                src="https://api.uifaces.co/our-content/donated/xZ4wg2Xj.jpg"
                className="w-10 h-10 rounded-full border-2 border-white"
              />
              <img
                src="https://images.unsplash.com/photo-*************-0a1dd7228f2d?ixlib=rb-0.3.5&q=80&fm=jpg&crop=faces&fit=crop&h=200&w=200&s=a72ca28288878f8404a795f39642a46f"
                className="w-10 h-10 rounded-full border-2 border-white"
              />
              <img
                src="https://randomuser.me/api/portraits/men/86.jpg"
                className="w-10 h-10 rounded-full border-2 border-white"
              />
              <img
                src="https://images.unsplash.com/photo-1510227272981-87123e259b17?ixlib=rb-0.3.5&q=80&fm=jpg&crop=faces&fit=crop&h=200&w=200&s=3759e09a5b9fbe53088b23c615b6312e"
                className="w-10 h-10 rounded-full border-2 border-white"
              />
              <p className="text-sm text-gray-400 font-medium translate-x-5">
                t('join-100-users')
              </p>
            </div>
          </div>
        </div>
        <div
          className="absolute inset-0 my-auto h-[500px]"
          style={{
            background:
              "linear-gradient(152.92deg, rgba(192, 132, 252, 0.2) 4.54%, rgba(232, 121, 249, 0.26) 34.2%, rgba(192, 132, 252, 0.1) 77.55%)",
            filter: "blur(118px)",
          }}
        ></div>
      </div>
      <div className="flex-1 flex items-center justify-center h-screen">
        <div className="w-full max-w-md space-y-8 px-4 bg-white text-gray-600 sm:px-0">
          <div className="grid grid-cols-3 gap-x-3"></div>
          {/* <div className="flex items-center justify-center">
            {clerkLoaded && <Loader2Icon className="animate-spin my-10" />}
          </div> */}

          {/* {!clerkLoaded && (
            <> */}
              <SignIn
                signUpUrl={`${process.env.NEXT_PUBLIC_APP_URL}/auth/register`}
                forceRedirectUrl={`${process.env.NEXT_PUBLIC_APP_URL}/enterprise/ai-agents/email-builder`}
                fallbackRedirectUrl={`${process.env.NEXT_PUBLIC_APP_URL}/enterprise/ai-agents/email-builder`}
                appearance={{
                  elements: {
                    card: t('bg-background-shadow-none-border-rounded-lg'),
                  },
                }}
                />
              <div className="text-center text-sm">
                <Link
                  href="/auth/forgot-password"
                  className="text-muted-foreground hover:text-foreground underline underline-offset-4"
                >
                  {t('forgot-your-password')}
                </Link>
              </div>
            {/* </>
          )} */}
        </div>
      </div>
    </main>
  );
}
