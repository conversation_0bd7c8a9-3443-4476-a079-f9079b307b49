"use client";

import { useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useRouter } from "next/navigation";
import { Check, ChevronLeft, ChevronRight, Upload } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useAuth } from "@clerk/nextjs";
import axiosInstance from "@/config/axios";
import { ONBOARD_ROUTE } from "@/utils/routes";
import { useTranslations } from "next-intl";


export default function OnboardingForm() {
  const [currentStep, setCurrentStep] = useState(1);
  const router = useRouter();
  const { getToken } = useAuth();

  const t = useTranslations()
  const steps = [
    { id: 1, name: t('personal-info') },
    { id: 2, name: t('professional') },
    { id: 3, name: t('preferences') },
  ];
  
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    bio: "",
    company: "",
    role: "",
    experience: "",
    interests: "",
    linkedin: "",
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target;
    setFormData((prev) => ({ ...prev, [id]: value }));
  };
  const handleSelectChange = (value: string) => {
    setFormData((prev) => ({ ...prev, experience: value }));
  };
  const handleSubmit = async () => {
    try {
      const token = await getToken();
      console.log(formData);
  
      const response = await axiosInstance.post(
        ONBOARD_ROUTE, 
        formData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
  
      if (response.status === 200) {
        router.push("/enterprise/ai-agents/email-builder");
      } else {
        console.error("Failed to submit:", response.data);
      }
    } catch (error: any) {
      console.error("Error submitting form:", error.message);
    }
  };


  useEffect(() => {
    console.log("test");
  }, []);

  const handleNext = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    } else {
      // Handle form completion
      handleSubmit();
      // router.push("/dashboard");
    }
  };
  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  return (
    <div className="mx-auto max-w-2xl px-4 py-16">
      {/* Progress Steps */}
      <div className="mb-12">
        <div className="flex items-center justify-center gap-4">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div
                className={`flex h-8 w-8 items-center justify-center rounded-full border-2 ${
                  currentStep >= step.id
                    ? "border-primary bg-primary text-primary-foreground"
                    : "border-muted-foreground text-muted-foreground"
                }`}
              >
                {currentStep > step.id ? (
                  <Check className="h-4 w-4" />
                ) : (
                  <span>{step.id}</span>
                )}
              </div>
              {index < steps.length - 1 && (
                <div
                  className={`mx-4 h-0.5 w-12 ${
                    currentStep > step.id ? "bg-primary" : "bg-border"
                  }`}
                />
              )}
            </div>
          ))}
        </div>
        <div className="mt-4 text-center">
          <h1 className="text-2xl font-bold">{t('complete-your-profile')}</h1>
          <p className="mt-2 text-sm text-muted-foreground">
            
            
          </p>
        </div>
      </div>

      {/* Form Steps */}

      <AnimatePresence mode="wait">
        <motion.div
          key={currentStep}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -20 }}
          transition={{ duration: 0.3 }}
        >
          {currentStep === 1 && (
            <div className="space-y-6">
              <div className="grid gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="firstName">{t('first-name')}</Label>
                  <Input id="firstName" placeholder={t('john')} value={formData.firstName} onChange={handleChange} />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="lastName">{t('last-name')}</Label>
                  <Input id="lastName" placeholder={t('doe')} value={formData.lastName} onChange={handleChange} />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="bio">{t('bio')}</Label>
                  <Textarea id="bio" placeholder={t('tell-us-about-yourself')} className="min-h-[100px]" value={formData.bio} onChange={handleChange} />
                </div>
              </div>
            </div>
          )}

          {currentStep === 2 && (
            <div className="space-y-6">
              <div className="grid gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="company">{t('company')}</Label>
                  <Input id="company" placeholder={t('acme-inc')} value={formData.company} onChange={handleChange} />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="role">{t('role')}</Label>
                  <Input id="role" placeholder={t('software-engineer')} value={formData.role} onChange={handleChange} />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="experience">{t('experience-level')}</Label>
                  <Select onValueChange={handleSelectChange}>
                    <SelectTrigger>
                      <SelectValue placeholder={formData.experience || t('select-experience-level')} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="junior">{t('junior-0-2-years')}</SelectItem>
                      <SelectItem value="mid">{t('mid-level-3-5-years')}</SelectItem>
                      <SelectItem value="senior">{t('senior-5-years')}</SelectItem>
                      <SelectItem value="lead">Lead/Architect</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          )}

          {currentStep === 3 && (
            <div className="space-y-6">
              <div className="grid gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="interests">{t('areas-of-interest')}</Label>
                  <Textarea id="interests" placeholder={t('e-g-web-development-ai-ml')} className="min-h-[100px]" value={formData.interests} onChange={handleChange} />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="linkedin">{t('linkedin-profile-optional')}</Label>
                  <Input id="linkedin" placeholder="https://linkedin.com/username" value={formData.linkedin} onChange={handleChange} />
                </div>
              </div>
            </div>
          )}

          <div className="mt-8 flex justify-between">
            <Button onClick={handlePrevious} className="flex items-center gap-2" disabled={currentStep === 1}>
              <ChevronLeft className="h-4 w-4" /> {t('previous')}
            </Button>

            <Button onClick={handleNext} className="flex items-center gap-2">
              {currentStep === steps.length ? t('complete') : t('next')}
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </motion.div>
      </AnimatePresence>
    </div>
  );
}
