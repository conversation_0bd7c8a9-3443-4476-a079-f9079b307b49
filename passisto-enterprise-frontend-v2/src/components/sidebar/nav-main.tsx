"use client";

import Link from "next/link";
import { ChevronRight } from "lucide-react";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/ui/sidebar";
import {
  Collapsible,
  CollapsibleTrigger,
  CollapsibleContent,
} from "../ui/collapsible";

import { userHasPermission } from "@/lib/utils";
import { useBackendUser } from '@/hooks/useBackendUser'; // Adjust the path as needed
import { useTranslations } from "next-intl";


export function NavMain({ items }: any) {
  const t = useTranslations()
  const { backendUser, loading, error } = useBackendUser();
  
  // const filteredItems = items.filter((item: any) => {
    

  //   if (item.permission && item.permission.length > 0) {
  //     if (!backendUser) return false;

  //     return userHasPermission(backendUser.permissions, item.permission);
  //   }
  //    console.log(item?.items)
  //   if (item?.items?.permission && item?.items?.permission.length > 0) {
  //     console.log(item.items.permission)
  //     if (!backendUser) return false;
    
      

  //     return userHasPermission(backendUser.permissions, item?.items?.permission);
  //   }
  //   return true;
  // });

const filteredItems = items
  .map((item: any) => {
    // If it has sub-items, filter them recursively
    if (item.items && Array.isArray(item.items)) {
      const filteredSubItems = item.items.filter((subItem: any) => {
        if (subItem.permission?.length > 0) {
          if (!backendUser) return false;
          return userHasPermission(backendUser.permissions, subItem.permission);
        }
        return true;
      });

      // Only return item if it has visible children or no permissions itself
      const showParent =
        (!item.permission?.length ||
          userHasPermission(backendUser?.permissions ?? [], item.permission)) &&
        filteredSubItems.length > 0;

      return showParent ? { ...item, items: filteredSubItems } : null;
    }

    // If no sub-items, check permission directly
    if (item.permission?.length > 0) {
      if (!backendUser) return false;
      return userHasPermission(backendUser.permissions, item.permission)
        ? item
        : null;
    }

    return item;
  })
  .filter(Boolean); // Remove null items



  return (
    <SidebarGroup>
      <SidebarGroupLabel>Platform</SidebarGroupLabel>
      <SidebarMenu>
        {filteredItems.map((item: any) => (
          <SidebarMenuItem key={item.title}>
            {item.items ? (
              <Collapsible
                asChild
                defaultOpen={item.isActive}
                className="group/collapsible"
              >
                <div>
                  <CollapsibleTrigger asChild>
                    <SidebarMenuButton tooltip={t(item.title)}>
                      {item.icon && <item.icon />}
                      <span className="text-sm opacity-100">{t(item.title)}</span>
                      <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                    </SidebarMenuButton>
                  </CollapsibleTrigger>
                  <CollapsibleContent>
                    <SidebarMenuSub>
                      {item.items.map((subItem: any) => (
                        <SidebarMenuSubItem key={t(subItem.title)}>
                          <SidebarMenuSubButton asChild>
                            <a
                              href={subItem.url}
                              className="text-base opacity-90"
                            >
                              {subItem.icon && <subItem.icon />}
                              <span>{t(subItem.title)}</span>
                            </a>
                          </SidebarMenuSubButton>
                        </SidebarMenuSubItem>
                      ))}
                    </SidebarMenuSub>
                  </CollapsibleContent>
                </div>
              </Collapsible>
            ) : (
              <Link href={item.url} passHref className={!item.disabled ? "" : "cursor-not-allowed pointer-events-none text-muted-foreground opacity-60"}>
                <SidebarMenuButton tooltip={item.title}>
                  {item.icon && <item.icon />}
                  <span className="text-sm opacity-100">{t(item.title)}</span>
                </SidebarMenuButton>
              </Link>
            )}
          </SidebarMenuItem>
        ))}
      </SidebarMenu>
    </SidebarGroup>
  );
}
