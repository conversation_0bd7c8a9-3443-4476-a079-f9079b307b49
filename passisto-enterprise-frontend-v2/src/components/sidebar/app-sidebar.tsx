"use client";
import {
  AudioWaveform,
  Bot,
  Command,
  Frame,
  GalleryVerticalEnd,
  Map,
  PieChart,
  Home,
  MessageCircle,
  Search,
  Users,
  Shield,
  Share,
  UsersRound,
  Database,
  FileText,
  Hammer,
  BotIcon,
  Folder,
  Clock,
  Trash2,
  MessageCirclePlus,
  Wrench,
} from "lucide-react";

import { NavMain } from "./nav-main";
import { NavUser } from "./nav-user";
import { TeamSwitcher } from "./team-switcher";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar";
import { ChatHistorySidebar } from "./chat-history-sidebar";
import { ROUTE_PERMISSIONS } from "@/utils/ROUTE_PERMISSIONS";

// This is sample data.
const data = {
  user: {
    name: "abdel<PERSON><PERSON>",
    email: "<EMAIL>",
    avatar: "/avatars/shadcn.jpg",
  },
  teams: [
    {
      name: "<PERSON><PERSON><PERSON>",
      logo: GalleryVerticalEnd,
      plan: "Enterprise",
    },
    {
      name: "Acme Corp.",
      logo: AudioWaveform,
      plan: "Startup",
    },
    {
      name: "Evil Corp.",
      logo: Command,
      plan: "Free",
    },
  ],
  navMain: [
    {
      title: "dashboard",
      url: "/enterprise/dashboard",
      icon: Home,
      isActive: true,
    },

    {
      disabled: false,
      title: "search",
      url: "/enterprise/search",
      icon: Search,
      permission:ROUTE_PERMISSIONS["/enterprise/search"],
    },
    {
      disabled: false,
      title: "users",
      url: "/enterprise/users",
      icon: Users,
      permission:ROUTE_PERMISSIONS["/enterprise/users"],
    },
    {
      disabled: false,
      title: "teams",
      url: "/enterprise/teams",
      icon: UsersRound,
      permission:ROUTE_PERMISSIONS["/enterprise/teams"],
    },
    {
      // disabled: false,
      title: "integrations",
      url: "/enterprise/integrations",
      icon: Share,
      permission:ROUTE_PERMISSIONS["/enterprise/integrations"],

    },
    // {
    //   disabled: false,
    //   title: "File Storage",
    //   url: "/enterprise/file-storage",
    //   icon: Database,
    //   items: [
    //     {
    //       title: "Files",
    //       url: "/enterprise/file-storage/files",
    //       icon: Folder, // Represents a folder for stored files
    //       permission:ROUTE_PERMISSIONS["/enterprise/file-storage/files"],
    //     },
    //     {
    //       title: "Shared with me",
    //       url: "#",
    //       icon: Share, // Represents shared files
    //     },
    //     {
    //       title: "Recent",
    //       url: "#",
    //       icon: Clock, // Represents recent activity
    //     },
    //     {
    //       title: "Trash",
    //       url: "/enterprise/file-storage/trash",
    //       icon: Trash2, // Represents deleted files
    //       permission:ROUTE_PERMISSIONS["/enterprise/file-storage/trash"],
    //     },
    //   ],
    // },
    {
      title: "ai-agent",
      url: "/enterprise/ai-agents",
      icon: Bot,
      items: [
        {
          title: "email-builder",
          url: "/enterprise/ai-agents/email-builder",
          icon: Hammer,
          permission:ROUTE_PERMISSIONS["/enterprise/ai-agents/email-builder"],
        },
        {
          title: "ai-interviewer",
          url: "/enterprise/ai-agents/ai-interviewer",
          icon: BotIcon,
          permission:ROUTE_PERMISSIONS["/enterprise/ai-agents/ai-interviewer"],

        },
        // {
        //   title: "Form Builder",
        //   url: "/enterprise/ai-agents/form-builder",
        //   icon: Wrench,
        //   permission:ROUTE_PERMISSIONS["/enterprise/ai-agents/form-builder"],
        // },
        // {
        //   title: "Workflow Automation",
        //   url: "/workflows",
        //   icon: FileText,
        //   permission:ROUTE_PERMISSIONS["/workflows"],
        // },
      ],
    },
    {
      disabled: false,
      title: "roles-and-permissions",
      url: "/enterprise/roles-permissions",
      icon: Shield,
      permission:ROUTE_PERMISSIONS["/enterprise/roles-permissions"],
    },
  ],

  navChat: [
    {
      title: "chat",
      url: "/enterprise/chat",
      icon: MessageCircle, // Updated icon for Chat
      permission:ROUTE_PERMISSIONS["/enterprise/chat"],
      items: [
        {
          title: "new-chat", // Button for creating new chat
          url: "/chat", // Link to the chat creation page
          icon: MessageCirclePlus,
        },
        {
          title: "today",
          icon: Folder,
          items: [
            { title: "AI Ethics Discussion", url: "/chat/today/ai-ethics" },
            { title: "Python vs JavaScript", url: "/chat/today/python-vs-js" },
          ],
        },
        {
          title: "Yesterday",
          icon: Folder,
          items: [
            {
              title: "Machine Learning Basics",
              url: "/chat/yesterday/ml-basics",
            },
            {
              title: "Web Development Tips",
              url: "/chat/yesterday/web-dev-tips",
            },
          ],
        },
        {
          title: "Previous Week",
          icon: Folder,
          items: [
            {
              title: "Data Structures in C++",
              url: "/chat/week/data-structures-cpp",
            },
            { title: "React Hooks Explained", url: "/chat/week/react-hooks" },
          ],
        },
        {
          title: "Previous Month",
          icon: Folder,
          items: [
            {
              title: "Quantum Computing Intro",
              url: "/chat/month/quantum-computing",
            },
            { title: "Blockchain Technology", url: "/chat/month/blockchain" },
          ],
        },
      ],
    },
  ],
  projects: [
    { name: "Design Engineering", url: "#", icon: Frame },
    { name: "Sales & Marketing", url: "#", icon: PieChart },
    { name: "Travel", url: "#", icon: Map },
  ],
};

export function AppSidebar(props: any) {
  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <TeamSwitcher teams={data.teams} />
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        <ChatHistorySidebar />
        {/* <NavProjects projects={data.projects} /> */}
      </SidebarContent>
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
