"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { HistoryIcon, HomeIcon, SettingsIcon, FileTextIcon, CheckSquareIcon, PencilRulerIcon } from "lucide-react"
import { cn } from "@/lib/utils"

interface DashboardNavProps {
  readonly collapsed?: boolean;
}

export function DashboardNav({ collapsed = false }: DashboardNavProps) {
  const pathname = usePathname()

  const items = [
    {
      title: "Dashboard",
      href: "/workflows/dashboard",
      icon: HomeIcon,
    },
    {
      title: "Workflows",
      href: "/workflows/dashboard/workflows",
      icon: FileTextIcon,
    },
    {
      title: "Workflow Designer",
      href: "/workflows/dashboard/workflows/designer",
      icon: PencilRulerIcon,
    },
    {
      title: "Workflow Runs",
      href: "/workflows/dashboard/workflow-runs",
      icon: HistoryIcon,
    },
    {
      title: "My Tasks",
      href: "/workflows/dashboard/my-tasks",
      icon: CheckSquareIcon,
    },
    {
      title: "Settings",
      href: "/workflows/dashboard/settings",
      icon: SettingsIcon,
    },
  ]

  return (
    <nav className={cn(
      "grid items-start gap-2 py-4",
      collapsed ? "px-1" : "px-2"
    )}>
      {items.map((item) => {
        const Icon = item.icon
        return (
          <Link
            key={item.href}
            href={item.href}
            className={cn(
              "group flex items-center rounded-md py-2 text-sm font-medium hover:bg-sidebar-accent",
              collapsed ? "justify-center px-2" : "px-3",
              pathname === item.href
                ? "bg-sidebar-accent text-sidebar-accent-foreground"
                : "text-sidebar-foreground"
            )}
            title={collapsed ? item.title : undefined}
          >
            <Icon className={cn(
              "h-5 w-5",
              collapsed ? "" : "mr-2"
            )} />
            {!collapsed && <span>{item.title}</span>}
          </Link>
        )
      })}
    </nav>
  )
}
