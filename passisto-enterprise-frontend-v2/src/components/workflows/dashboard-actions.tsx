"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { useRouter } from "next/navigation"
import { HistoryIcon, FileTextIcon, PlusIcon } from "lucide-react"

interface DashboardActionsProps {
  type: "workflows" | "workflow-runs"
}

export function DashboardActions({ type }: DashboardActionsProps) {
  const router = useRouter()

  if (type === "workflows") {
    return (
      <div className="flex space-x-2">
        <Button onClick={() => router.push("/workflows/dashboard/workflows")}>
          <FileTextIcon className="mr-2 h-4 w-4" />
          View Workflows
        </Button>
        <Button variant="outline" onClick={() => router.push("/workflows/dashboard/workflows/new")}>
          <PlusIcon className="mr-2 h-4 w-4" />
          Create New
        </Button>
      </div>
    )
  }

  if (type === "workflow-runs") {
    return (
      <Button onClick={() => router.push("/dashboard/workflow-runs")}>
        <HistoryIcon className="mr-2 h-4 w-4" />
        View Workflow Runs
      </Button>
    )
  }

  return null
}
