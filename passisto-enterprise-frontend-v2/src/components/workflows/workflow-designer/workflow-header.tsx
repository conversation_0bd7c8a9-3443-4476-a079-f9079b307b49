"use client";

import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Save, ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";

interface WorkflowHeaderProps {
  workflowTitle: string;
  isEditingTitle: boolean;
  setWorkflowTitle: (title: string) => void;
  setIsEditingTitle: (isEditing: boolean) => void;
  handleTitleChange: (title: string) => void;
  openSaveDialog: () => void;
}

export function WorkflowHeader({
  workflowTitle,
  isEditingTitle,
  setWorkflowTitle,
  setIsEditingTitle,
  handleTitleChange,
  openSaveDialog,
}: Readonly<WorkflowHeaderProps>) {
  const router = useRouter();
  return (
    <div className="py-1 px-2 border-b flex items-center bg-white h-10 w-full overflow-hidden">
      <Button
        variant="ghost"
        size="icon"
        onClick={() => router.push("/workflows/dashboard/workflows")}
        className="mr-2"
        aria-label="Back to workflows"
      >
        <ArrowLeft className="h-4 w-4" />
      </Button>

      {isEditingTitle ? (
        <Input
          value={workflowTitle}
          onChange={(e) => setWorkflowTitle(e.target.value)}
          onBlur={() => handleTitleChange(workflowTitle)}
          onKeyDown={(e) => e.key === "Enter" && handleTitleChange(workflowTitle)}
          className="max-w-md font-semibold text-sm h-7 flex-1"
          autoFocus
        />
      ) : (
        <button
          type="button"
          className="font-semibold text-sm text-left hover:bg-gray-100 px-2 py-0 rounded border-0 bg-transparent h-7 max-w-[60%] overflow-hidden text-ellipsis whitespace-nowrap"
          onClick={() => setIsEditingTitle(true)}
          onKeyDown={(e) => e.key === "Enter" && setIsEditingTitle(true)}
        >
          {workflowTitle}
          <span className="ml-2 text-xs text-gray-500">(click to edit)</span>
        </button>
      )}

      <div className="ml-auto flex-shrink-0 flex gap-2">
        <Button variant="outline" size="sm" onClick={openSaveDialog}>
          <Save className="h-4 w-4 mr-2" /> Save As...
        </Button>
      </div>
    </div>
  );
}
