"use client";

import Link from "next/link";
import { useState } from "react";
import {
  RefreshCw,
  Clock,
  User,
  Calendar,
  Edit,
  Trash2,
  ExternalLink,
} from "lucide-react";
import { toast } from "react-toastify";
import axiosInstance from "@/config/axios";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { ToastContainer } from "react-toastify";
import { DeleteIntegrationDialog } from "@/components/integrations/delete-integration-dialog";
import { ALL_INTEGRATIONS } from "@/utils/routes";
import { useAuth } from "@clerk/nextjs";
import { integrationPermissions } from "@/utils/ACTION_PERMISSIONS";
import { useBackendUser } from "@/hooks/useBackendUser";
import { useTranslations } from "next-intl";

// Centralized API base path
interface BaseIntegration {
  id: string;
  name: string;
  providerType: "jira" | "ftp" | "web";
  status: "loading" | "completed" | "failed" | "refreshing" | "not updated";
  updateTime: number;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

interface JiraIntegration extends BaseIntegration {
  providerType: "jira";
  jira: {
    domain: string;
    project: string;
    email: string;
  };
}

interface FtpIntegration extends BaseIntegration {
  providerType: "ftp";
  ftp: {
    server: string;
    port: number;
    username: string;
    isSecure: boolean;
  };
}

interface WebIntegration extends BaseIntegration {
  providerType: "web";
  web: {
    url: string;
  };
}

type Integration = JiraIntegration | FtpIntegration | WebIntegration;

interface IntegrationDetailsProps {
  integration: Integration;
  onDelete?: () => void;
}

/**
 * Info field component to display labeled content
 */
function Info({
  label,
  value,
  icon,
  children,
}: {
  label: string;
  value?: string | number | boolean;
  icon?: React.ReactNode;
  children?: React.ReactNode;
}) {
  const t = useTranslations();
  return (
    <div className="flex flex-col space-y-1">
      <span className="text-sm font-medium text-muted-foreground">{label}</span>
      <span className="flex items-center">
        {icon}
        {children ??
          (typeof value === "boolean" ? (value ? t('yes') : t('no')) : value)}
      </span>
    </div>
  );
}

export function IntegrationDetails({
  integration,
  onDelete,
}: IntegrationDetailsProps) {
  const t = useTranslations();
  const [isRetrying, setIsRetrying] = useState(false);

  /**
   * Format date string to locale format
   */
  const formatDate = (dateString: string) =>
    new Date(dateString).toLocaleString();
  const { getToken } = useAuth();
  const { backendUser } = useBackendUser();

  /**
   * Handle retry action for failed integrations
   */
  const handleRetry = async () => {
    const token = await getToken();
    setIsRetrying(true);
    try {
      const token = await getToken();
      const headers = {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      };
      const endpoint = `${ALL_INTEGRATIONS}/${integration.providerType}/${integration.id}/retry`;
      await axiosInstance.post(endpoint, headers);
      toast.success(`Retry for "${integration.name}" initiated successfully!`);
    } catch (error) {
      toast.error(`Failed to retry "${integration.name}". Please try again.`);
      console.error("Retry failed:", error);
    } finally {
      setIsRetrying(false);
    }
  };

  /**
   * Render configuration based on integration type
   */
  const renderConfiguration = () => {
    switch (integration.providerType) {
      case "jira":
        return (
          <>
            <Info label={t('domain')} value={integration.jira.domain} />
            <Info label={t('project')} value={integration.jira.project} />
            <Info label={t('email')} value={integration.jira.email} />
          </>
        );
      case "ftp":
        return (
          <>
            <Info label={t('server')} value={integration.ftp.server} />
            <Info label={t('port')} value={integration.ftp.port} />
            <Info label={t('username')} value={integration.ftp.username} />
            <Info label={t('secure-connection')} value={integration.ftp.isSecure} />
          </>
        );
      case "web":
        return (
          <>
            <Info label="URL" value={integration.web.url} />
            <Button
              variant="outline"
              size="sm"
              className="mt-2"
              onClick={() => window.open(integration.web.url, "_blank")}
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              {t('visit-website')}
            </Button>
          </>
        );
      default:
        return (
          <p className="text-sm text-muted-foreground">
            {t('no-configuration-available')}
          </p>
        );
    }
  };

  /**
   * Determine if retry action is available
   */
  const canRetry =
    integration.status.toLowerCase() === "failed" &&
    integration.providerType !== "web"; // Web integrations don't support retry

  /**
   * Map status to colors based on `IntegrationStatus` component
   */
  const statusColors = {
    loading: "bg-blue-500 text-white",
    completed: "bg-green-500 text-white",
    failed: "bg-red-500 text-white",
    refreshing: "bg-cyan-500 text-white",
    not_updated: "bg-yellow-500 text-white",
  };

  const statusKey =
    integration.status.toLowerCase() as keyof typeof statusColors;
  const statusColor = statusColors[statusKey] || "bg-gray-500 text-white";

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>{t('integration-status-guide')}</CardTitle>
              <CardDescription>
                {t('understand-what-each-status-means-in-simple-words')}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4 text-sm text-muted-foreground">
              <div>
                <span className="font-medium text-blue-600">Loading:</span> {t('this-means-your-integration-just-started-collecting-information-its-the-first-step-after-setting-it-up')}
              </div>
              <div>
                <span className="font-medium text-green-600">Completed:</span>{" "}
                {t('everything-worked-perfectly-your-integration-successfully-brought-in-the-data')}
              </div>
              <div>
                <span className="font-medium text-red-600">Failed:</span>{" "}
                {t('something-went-wrong-the-integration-couldnt-fetch-the-data-properly')}
              </div>
              <div>
                <span className="font-medium text-cyan-600">Refreshing:</span>{" "}
                {t('the-integration-already-worked-before-and-now-its-trying-to-update-its-data-again')}
              </div>
              <div>
                <span className="font-medium text-yellow-600">
                  {t('not-updated')}
                </span>{" "}
                {t('the-system-tried-to-refresh-the-data-but-it-didnt-work-this-time-it-stayed-as-it-was-before')}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <div>
                <CardTitle className="text-2xl font-bold">
                  {integration.name}
                </CardTitle>
                <CardDescription className="text-sm text-muted-foreground">
                  {integration.providerType.toUpperCase()} {t('integration')}
                </CardDescription>
              </div>
              <Badge
                className={`px-3 py-1 rounded-full text-xs font-medium ${statusColor}`}
              >
                {integration.status.replace("_", " ").toUpperCase()}
              </Badge>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="overview" className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="overview">{t('overview')}</TabsTrigger>
                  <TabsTrigger value="configuration">{t('configuration')}</TabsTrigger>
                </TabsList>

                <TabsContent value="overview" className="mt-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Info
                      label={t('update-frequency')}
                      value={`Every ${integration.updateTime} days`}
                      icon={
                        <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                      }
                    />
                    <Info
                      label={t('created-by')}
                      value={integration.createdBy}
                      icon={
                        <User className="mr-2 h-4 w-4 text-muted-foreground" />
                      }
                    />
                    <Info
                      label={t('created-at')}
                      value={formatDate(integration.createdAt)}
                      icon={
                        <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                      }
                    />
                    <Info
                      label={t('last-rerun')}
                      value={formatDate(integration.updatedAt)}
                      icon={
                        <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                      }
                    />
                  </div>
                </TabsContent>

                <TabsContent value="configuration" className="mt-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {renderConfiguration()}
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
            <CardFooter className="flex justify-between">
              {integration.providerType === "ftp" &&
                integrationPermissions.canUpdateFTP(
                  backendUser?.permissions || []
                ) && (
                  <Button variant="outline" asChild>
                    <Link
                      href={`/enterprise/integrations/${integration.id}/edit`}
                    >
                      <Edit className="mr-2 h-4 w-4" />
                      {t('edit')}
                    </Link>
                  </Button>
                )}
              {integration.providerType === "jira" &&
                integrationPermissions.canUpdateJira(
                  backendUser?.permissions || []
                ) && (
                  <Button variant="outline" asChild>
                    <Link
                      href={`/enterprise/integrations/${integration.id}/edit`}
                    >
                      <Edit className="mr-2 h-4 w-4" />
                      {t('edit')}
                    </Link>
                  </Button>
                )}
              {integration.providerType === "web" &&
                integrationPermissions.canUpdateWeb(
                  backendUser?.permissions || []
                ) && (
                  <Button variant="outline" asChild>
                    <Link
                      href={`/enterprise/integrations/${integration.id}/edit`}
                    >
                      <Edit className="mr-2 h-4 w-4" />
                      {t('edit')}
                    </Link>
                  </Button>
                )}
              
             
              {canRetry && (
                <Button onClick={handleRetry} disabled={isRetrying}>
                  <RefreshCw
                    className={`mr-2 h-4 w-4 ${
                      isRetrying ? "animate-spin" : ""
                    }`}
                  />
                  {isRetrying ? t('retrying') : t('retry')}
                </Button>
              )}
            </CardFooter>
          </Card>
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>{t('status')}</CardTitle>
              <CardDescription>{t('current-integration-status')}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col items-center justify-center py-4 space-y-4">
                <div
                  className={`w-20 h-20 rounded-full flex items-center justify-center ${statusColor}`}
                >
                  <span className="text-white font-medium">
                    {integration.status.replace("_", " ").toUpperCase()}
                  </span>
                </div>
                <div className="text-center">
                  <p className="font-medium">{t('last-rerun')}</p>
                  <p className="text-sm text-muted-foreground">
                    {formatDate(integration.updatedAt)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>{t('quick-actions')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {canRetry && (
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={handleRetry}
                  disabled={isRetrying}
                >
                  <RefreshCw
                    className={`mr-2 h-4 w-4 ${
                      isRetrying ? "animate-spin" : ""
                    }`}
                  />
                  {isRetrying ? t('retrying') : t('retry-integration')}
                </Button>
              )}

              {integration.providerType === "ftp" &&
                integrationPermissions.canUpdateFTP(
                  backendUser?.permissions || []
                ) && (
                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    asChild
                  >
                    <Link
                      href={`/enterprise/integrations/${integration.id}/edit`}
                    >
                      <Edit className="mr-2 h-4 w-4" />
                      {t('edit-integration')}
                    </Link>
                  </Button>
                )}

              {integration.providerType === "jira" &&
                integrationPermissions.canUpdateJira(
                  backendUser?.permissions || []
                ) && (
                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    asChild
                  >
                    <Link
                      href={`/enterprise/integrations/${integration.id}/edit`}
                    >
                      <Edit className="mr-2 h-4 w-4" />
                      {t('edit-integration')}
                    </Link>
                  </Button>
                )}

              {integration.providerType === "web" &&
                integrationPermissions.canUpdateWeb(
                  backendUser?.permissions || []
                ) && (
                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    asChild
                  >
                    <Link
                      href={`/enterprise/integrations/${integration.id}/edit`}
                    >
                      <Edit className="mr-2 h-4 w-4" />
                      {t('edit-integration')}
                    </Link>
                  </Button>
                )}

              {/* <Button variant="outline" className="w-full justify-start" asChild>
                <Link href={`/enterprise/integrations/${integration.id}/edit`}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Integration
                </Link>
              </Button> */}

              {/* Reuse DeleteIntegrationDialog */}
              <DeleteIntegrationDialog
                integration={integration}
                onDelete={() => {
                  // Redirect to integrations page after deletion
                  window.location.href = "/enterprise/integrations";
                }}
              />
            </CardContent>
          </Card>
        </div>
      </div>
      <ToastContainer />
    </>
  );
}
