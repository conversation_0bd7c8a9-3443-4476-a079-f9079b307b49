import { userHasPermission } from "@/lib/utils";

// User management permissions
export const userPermissions = {
  canView: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], [
      "CAN_VIEW_USER",
      "CAN_MANAGE_USERS",
      "CAN_UPDATE_USER",
      "CAN_CREATE_USER",
      "CAN_DELETE_USER",
    ]),

  canCreate: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], [
      "CAN_CREATE_USER",
      "CAN_MANAGE_USERS",
    ]),

  canUpdate: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], [
      "CAN_UPDATE_USER",
      "CAN_MANAGE_USERS",
    ]),

  canDelete: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], [
      "CAN_DELETE_USER",
      "CAN_MANAGE_USERS",
    ]),

  canInvite: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], [
      "CAN_INVITE_USER",
      "CAN_MANAGE_USERS",
    ]),

  canToggleStatus: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], [
      "CAN_TOGGLE_USER_STATUS",
      "CAN_MANAGE_USERS",
    ]),

  canCompleteProfile: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], [
      "CAN_COMPLETE_PROFILE_USER",
      "CAN_MANAGE_USERS",
    ]),
};

// Role and permission management
export const rolePermissions = {
  canManageRoles: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], ["CAN_MANAGE_ROLES"]),

  canManagePermissions: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], ["CAN_MANAGE_PERMISSIONS"]),
};

// Team management
export const teamPermissions = {
  canCreate: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], [
      "CAN_CREATE_GROUP",
      "CAN_MANAGE_GROUPS",
    ]),

  canUpdate: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], [
      "CAN_UPDATE_GROUP",
      "CAN_MANAGE_GROUPS",
    ]),

  canDelete: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], [
      "CAN_DELETE_GROUP",
      "CAN_MANAGE_GROUPS",
    ]),

  canView: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], [
      "CAN_VIEW_GROUP",
      "CAN_MANAGE_GROUPS",
      "CAN_UPDATE_GROUP",
      "CAN_CREATE_GROUP",
      "CAN_ASSIGN_USER_TO_GROUP",
      "CAN_REMOVE_USER_FROM_GROUP",
      "CAN_ASSIGN_INTEGRATION_TO_GROUP",
      "CAN_REMOVE_INTEGRATION_FROM_GROUP",
    ]),

  canAssignUser: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], [
      "CAN_ASSIGN_USER_TO_GROUP",
      "CAN_MANAGE_GROUPS",
    ]),

  canRemoveUser: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], [
      "CAN_REMOVE_USER_FROM_GROUP",
      "CAN_MANAGE_GROUPS",
    ]),

  canManageTeamIntegration: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], [
      "CAN_MANAGE_GROUPS",
      "CAN_VIEW_GROUP",
      "CAN_MANAGE_INTEGRATIONS",
      "CAN_READ_ALL_INTEGRATIONS",
      "CAN_ASSIGN_INTEGRATION_TO_GROUP",
      "CAN_REMOVE_INTEGRATION_FROM_GROUP",
    ]),
};

// Integration management
export const integrationPermissions = {
  canView: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], [
      "CAN_VIEW_INTEGRATION",
      "CAN_MANAGE_INTEGRATIONS",

      // FTP
      "CAN_CREATE_FTP",
      "CAN_UPDATE_FTP",
      "CAN_DELETE_FTP",

      // Jira
      "CAN_CREATE_JIRA",
      "CAN_UPDATE_JIRA",
      "CAN_DELETE_JIRA",

      // Web
      "CAN_CREATE_WEB",
      "CAN_UPDATE_WEB",
      "CAN_DELETE_WEB",
    ]),

  canAssignToGroup: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], [
      "CAN_ASSIGN_INTEGRATION_TO_GROUP",
      "CAN_MANAGE_INTEGRATIONS",
    ]),

  canRemoveFromGroup: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], [
      "CAN_REMOVE_INTEGRATION_FROM_GROUP",
      "CAN_MANAGE_INTEGRATIONS",
    ]),

  canCreateFTP: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], [
      "CAN_CREATE_FTP",
      "CAN_MANAGE_INTEGRATIONS",
    ]),

  canUpdateFTP: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], [
      "CAN_UPDATE_FTP",
      "CAN_MANAGE_INTEGRATIONS",
    ]),

  canDeleteFTP: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], [
      "CAN_DELETE_FTP",
      "CAN_MANAGE_INTEGRATIONS",
    ]),

  canCreateJira: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], [
      "CAN_CREATE_JIRA",
      "CAN_MANAGE_INTEGRATIONS",
    ]),

  canUpdateJira: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], [
      "CAN_UPDATE_JIRA",
      "CAN_MANAGE_INTEGRATIONS",
    ]),

  canDeleteJira: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], [
      "CAN_DELETE_JIRA",
      "CAN_MANAGE_INTEGRATIONS",
    ]),

  canCreateWeb: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], [
      "CAN_CREATE_WEB",
      "CAN_MANAGE_INTEGRATIONS",
    ]),

  canUpdateWeb: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], [
      "CAN_UPDATE_WEB",
      "CAN_MANAGE_INTEGRATIONS",
    ]),

  canDeleteWeb: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], [
      "CAN_DELETE_WEB",
      "CAN_MANAGE_INTEGRATIONS",
    ]),

  canCreateAll: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], [
      "CAN_CREATE_WEB",
      "CAN_CREATE_FTP",
      "CAN_CREATE_JIRA",
      "CAN_MANAGE_INTEGRATIONS",
    ]),
};

// Email management
export const emailBuilderPermissions = {
  canEnhanceDescription: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], [
      "CAN_ENHANCE_EMAIL_BUILDER_DESCRIPTION",
      "CAN_MANAGE_EMAIL_BUILDER",
    ]),

  canGenerateTemplate: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], [
      "CAN_GENERATE_EMAIL_BUILDER_TEMPLATE",
      "CAN_MANAGE_EMAIL_BUILDER",
    ]),

  canViewTemplate: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], [
      "CAN_VIEW_EMAIL_BUILDER_TEMPLATE",
      "CAN_MANAGE_EMAIL_BUILDER",
      "CAN_EDIT_EMAIL_BUILDER",
      "CAN_DELETE_EMAIL_BUILDER",
      "CAN_SEND_EMAIL_BUILDER",
    ]),

  canEditBuilder: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], [
      "CAN_EDIT_EMAIL_BUILDER",
      "CAN_MANAGE_EMAIL_BUILDER",
    ]),

  canDeleteBuilder: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], [
      "CAN_DELETE_EMAIL_BUILDER",
      "CAN_MANAGE_EMAIL_BUILDER",
    ]),

  canSendBuilderEmail: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], [
      "CAN_SEND_EMAIL_BUILDER",
      "CAN_MANAGE_EMAIL_BUILDER",
    ]),
};

// Interview management
export const interviewPermissions = {
  canCreate: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], [
      "CAN_CREATE_INTERVIEW",
      "CAN_MANAGE_INTERVIEW_AGENT",
    ]),

  canUpdate: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], [
      "CAN_UPDATE_INTERVIEW",
      "CAN_MANAGE_INTERVIEW_AGENT",
    ]),

  canDelete: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], [
      "CAN_DELETE_INTERVIEW",
      "CAN_MANAGE_INTERVIEW_AGENT",
    ]),

  canView: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], [
      "CAN_VIEW_INTERVIEW",
      "CAN_MANAGE_INTERVIEW_AGENT",
      "CAN_UPDATE_INTERVIEW",
      "CAN_DELETE_INTERVIEW",
    ]),

  canSendToCandidate: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], [
      "CAN_SEND_INTERVIEW_TO_CANDIDATE",
      "CAN_MANAGE_INTERVIEW_AGENT",
    ]),

  canViewFeedback: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], [
      "CAN_VIEW_FEEDBACK",
      "CAN_MANAGE_INTERVIEW_AGENT",
    ]),
};

// Form management
export const formPermissions = {
  canManage: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], [
      "CAN_CREATE_FORM",
      "CAN_UPDATE_FORM",
      "CAN_DELETE_FORM",
      "CAN_VIEW_FORM",
    ]),

  canPublish: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], ["CAN_PUBLISH_FORM"]),
};

// Billing management
export const billingPermissions = {
  canManage: (userPermissions: string[]) =>
    userHasPermission(userPermissions ?? [], [
      "CAN_VIEW_PLANS",
      "CAN_VIEW_SUBSCRIPTION_STATUS",
      "CAN_VIEW_SUBSCRIPTION_DETAILS",
      "CAN_CREATE_CHECKOUT_SESSION",
      "CAN_CREATE_PORTAL_SESSION",
    ]),
};

// Helper to check multiple permissions at once
export const checkPermissions = (
  userPermissions: string[],
  permissionChecks: Array<(permissions: string[]) => boolean>
): boolean => {
  return permissionChecks.some((check) => check(userPermissions));
};

// checkPermissions(currentUserPermissions, [
//   userPermissions.canCreateUser,
//   teamPermissions.canViewTeam,
// ]);
